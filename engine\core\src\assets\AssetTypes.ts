/**
 * DL-Engine 资产类型定义
 */

import { Texture, Material, BufferGeometry, Object3D, AnimationClip, Audio } from 'three'

/**
 * 资产类型枚举
 */
export enum AssetType {
  /** 3D模型 */
  MODEL = 'model',
  /** 纹理 */
  TEXTURE = 'texture',
  /** 材质 */
  MATERIAL = 'material',
  /** 几何体 */
  GEOMETRY = 'geometry',
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 图片 */
  IMAGE = 'image',
  /** 动画 */
  ANIMATION = 'animation',
  /** 场景 */
  SCENE = 'scene',
  /** 字体 */
  FONT = 'font',
  /** 着色器 */
  SHADER = 'shader',
  /** 脚本 */
  SCRIPT = 'script',
  /** 数据 */
  DATA = 'data'
}

/**
 * 资产状态
 */
export enum AssetStatus {
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 加载中 */
  LOADING = 'loading',
  /** 已加载 */
  LOADED = 'loaded',
  /** 加载失败 */
  ERROR = 'error',
  /** 已缓存 */
  CACHED = 'cached'
}

/**
 * 资产优先级
 */
export enum AssetPriority {
  /** 低优先级 */
  LOW = 0,
  /** 普通优先级 */
  NORMAL = 1,
  /** 高优先级 */
  HIGH = 2,
  /** 紧急优先级 */
  URGENT = 3
}

/**
 * 资产接口
 */
export interface Asset {
  /** 资产ID */
  id: string
  
  /** 资产名称 */
  name: string
  
  /** 资产类型 */
  type: AssetType
  
  /** 资产URL */
  url: string
  
  /** 资产数据 */
  data: any
  
  /** 资产状态 */
  status: AssetStatus
  
  /** 文件大小（字节） */
  size: number
  
  /** MIME类型 */
  mimeType: string
  
  /** 加载进度 (0-1) */
  progress: number
  
  /** 错误信息 */
  error?: string
  
  /** 创建时间 */
  createdAt: Date
  
  /** 最后访问时间 */
  lastAccessedAt: Date
  
  /** 引用计数 */
  refCount: number
  
  /** 是否可缓存 */
  cacheable: boolean
  
  /** 缓存TTL（毫秒） */
  cacheTTL: number
  
  /** 元数据 */
  metadata: Record<string, any>
  
  /** 依赖资产 */
  dependencies: string[]
  
  /** 标签 */
  tags: string[]
}

/**
 * 模型资产
 */
export interface ModelAsset extends Asset {
  type: AssetType.MODEL
  data: {
    scene: Object3D
    animations: AnimationClip[]
    materials: Material[]
    geometries: BufferGeometry[]
    textures: Texture[]
  }
}

/**
 * 纹理资产
 */
export interface TextureAsset extends Asset {
  type: AssetType.TEXTURE
  data: Texture
  metadata: {
    width: number
    height: number
    format: string
    colorSpace: string
    flipY: boolean
    generateMipmaps: boolean
  }
}

/**
 * 音频资产
 */
export interface AudioAsset extends Asset {
  type: AssetType.AUDIO
  data: AudioBuffer
  metadata: {
    duration: number
    sampleRate: number
    numberOfChannels: number
    bitRate?: number
  }
}

/**
 * 材质资产
 */
export interface MaterialAsset extends Asset {
  type: AssetType.MATERIAL
  data: Material
  metadata: {
    materialType: string
    transparent: boolean
    doubleSided: boolean
    textureReferences: string[]
  }
}

/**
 * 几何体资产
 */
export interface GeometryAsset extends Asset {
  type: AssetType.GEOMETRY
  data: BufferGeometry
  metadata: {
    vertexCount: number
    faceCount: number
    hasUVs: boolean
    hasNormals: boolean
    hasTangents: boolean
    hasColors: boolean
  }
}

/**
 * 动画资产
 */
export interface AnimationAsset extends Asset {
  type: AssetType.ANIMATION
  data: AnimationClip
  metadata: {
    duration: number
    fps: number
    trackCount: number
    looping: boolean
  }
}

/**
 * 资产加载选项
 */
export interface AssetLoadOptions {
  /** 优先级 */
  priority?: AssetPriority
  
  /** 是否缓存 */
  cache?: boolean
  
  /** 缓存TTL */
  cacheTTL?: number
  
  /** 超时时间（毫秒） */
  timeout?: number
  
  /** 重试次数 */
  retries?: number
  
  /** 进度回调 */
  onProgress?: (progress: number) => void
  
  /** 错误回调 */
  onError?: (error: Error) => void
  
  /** 完成回调 */
  onComplete?: (asset: Asset) => void
  
  /** 额外的加载参数 */
  params?: Record<string, any>
}

/**
 * 资产查询选项
 */
export interface AssetQueryOptions {
  /** 资产类型过滤 */
  types?: AssetType[]
  
  /** 状态过滤 */
  statuses?: AssetStatus[]
  
  /** 标签过滤 */
  tags?: string[]
  
  /** 名称搜索 */
  namePattern?: string
  
  /** 排序字段 */
  sortBy?: 'name' | 'type' | 'size' | 'createdAt' | 'lastAccessedAt'
  
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  
  /** 分页 */
  limit?: number
  offset?: number
}

/**
 * 资产统计信息
 */
export interface AssetStats {
  /** 总资产数量 */
  totalAssets: number
  
  /** 按类型分组的数量 */
  assetsByType: Record<AssetType, number>
  
  /** 按状态分组的数量 */
  assetsByStatus: Record<AssetStatus, number>
  
  /** 总大小（字节） */
  totalSize: number
  
  /** 缓存大小（字节） */
  cacheSize: number
  
  /** 缓存命中率 */
  cacheHitRate: number
  
  /** 平均加载时间（毫秒） */
  averageLoadTime: number
  
  /** 内存使用情况 */
  memoryUsage: {
    textures: number
    geometries: number
    materials: number
    audio: number
    total: number
  }
}

/**
 * 资产事件类型
 */
export enum AssetEventType {
  /** 资产开始加载 */
  LOAD_START = 'asset:load:start',
  /** 资产加载进度 */
  LOAD_PROGRESS = 'asset:load:progress',
  /** 资产加载完成 */
  LOAD_COMPLETE = 'asset:load:complete',
  /** 资产加载失败 */
  LOAD_ERROR = 'asset:load:error',
  /** 资产被缓存 */
  CACHED = 'asset:cached',
  /** 资产从缓存移除 */
  UNCACHED = 'asset:uncached',
  /** 资产被引用 */
  REFERENCED = 'asset:referenced',
  /** 资产引用被释放 */
  UNREFERENCED = 'asset:unreferenced',
  /** 资产被销毁 */
  DISPOSED = 'asset:disposed'
}

/**
 * 资产事件数据
 */
export interface AssetEvent {
  type: AssetEventType
  assetId: string
  asset?: Asset
  progress?: number
  error?: Error
  timestamp: number
}

/**
 * 资产工厂函数类型
 */
export type AssetFactory<T extends Asset = Asset> = (
  id: string,
  url: string,
  options?: AssetLoadOptions
) => Promise<T>

/**
 * 资产处理器类型
 */
export type AssetProcessor<T = any> = (
  data: T,
  asset: Asset,
  options?: AssetLoadOptions
) => Promise<T> | T
