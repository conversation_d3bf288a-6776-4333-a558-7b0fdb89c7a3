# DL-Engine 资产系统

DL-Engine 的资产系统提供了完整的资产加载、管理和缓存功能，支持多种资产类型包括3D模型、纹理、音频等。

## 功能特性

- 🚀 **高性能加载**: 支持并发加载和优先级队列
- 💾 **智能缓存**: 多种缓存策略和自动清理
- 🔄 **状态管理**: 基于 hookstate 的响应式状态管理
- 📦 **多格式支持**: GLTF/GLB、各种纹理格式、音频格式
- 🎯 **类型安全**: 完整的 TypeScript 类型定义
- 🔧 **可扩展**: 支持自定义加载器和处理器

## 快速开始

### 基本使用

```typescript
import { AssetManager, AssetType } from '@dl-engine/engine-core/assets'

// 获取资产管理器实例
const assetManager = AssetManager.getInstance()

// 加载纹理
const texture = await assetManager.load(
  'my-texture',
  '/textures/diffuse.jpg',
  AssetType.TEXTURE
)

// 加载3D模型
const model = await assetManager.load(
  'my-model',
  '/models/character.glb',
  AssetType.MODEL
)

// 加载音频
const audio = await assetManager.load(
  'background-music',
  '/audio/music.mp3',
  AssetType.AUDIO
)
```

### 批量加载

```typescript
const assets = await assetManager.loadBatch([
  {
    id: 'texture1',
    url: '/textures/wall.jpg',
    type: AssetType.TEXTURE
  },
  {
    id: 'model1',
    url: '/models/building.glb',
    type: AssetType.MODEL
  },
  {
    id: 'sound1',
    url: '/audio/footstep.wav',
    type: AssetType.AUDIO
  }
])
```

### 带选项的加载

```typescript
const texture = await assetManager.load(
  'hdr-texture',
  '/textures/environment.hdr',
  AssetType.TEXTURE,
  {
    priority: AssetPriority.HIGH,
    cache: true,
    cacheTTL: 60 * 60 * 1000, // 1小时
    onProgress: (progress) => {
      console.log(`加载进度: ${Math.round(progress * 100)}%`)
    },
    onComplete: (asset) => {
      console.log('加载完成:', asset.name)
    }
  }
)
```

## 资产类型

### 纹理资产

```typescript
import { TextureLoader } from '@dl-engine/engine-core/assets'

// 创建纹理加载器
const textureLoader = new TextureLoader()

// 加载纹理
const textureAsset = await textureLoader.load(
  'my-texture',
  '/textures/diffuse.jpg',
  AssetType.TEXTURE,
  {
    flipY: false,
    generateMipmaps: true,
    colorSpace: 'srgb'
  }
)

// 访问Three.js纹理对象
const threeTexture = textureAsset.data
```

### 3D模型资产

```typescript
import { GLTFLoader } from '@dl-engine/engine-core/assets'

// 创建GLTF加载器
const gltfLoader = new GLTFLoader()

// 加载模型
const modelAsset = await gltfLoader.load(
  'character',
  '/models/character.glb',
  AssetType.MODEL,
  {
    enableDraco: true,
    dracoDecoderPath: '/draco/',
    autoPlayAnimations: true,
    generateBoundingBox: true
  }
)

// 访问模型数据
const { scene, animations, materials } = modelAsset.data
```

### 音频资产

```typescript
import { AudioLoader } from '@dl-engine/engine-core/assets'

// 创建音频加载器
const audioLoader = new AudioLoader(audioContext)

// 加载音频
const audioAsset = await audioLoader.load(
  'music',
  '/audio/background.mp3',
  AssetType.AUDIO,
  {
    decode: true,
    volume: 0.8,
    loop: true
  }
)

// 播放音频
const source = await audioLoader.playAudio(audioAsset, audioContext, {
  volume: 0.5,
  loop: true
})
```

## 缓存管理

### 缓存配置

```typescript
const assetManager = AssetManager.getInstance({
  cache: {
    maxSize: 1024 * 1024 * 1024, // 1GB
    strategy: CacheStrategy.LRU,
    defaultTTL: 30 * 60 * 1000, // 30分钟
    enablePersistence: true
  }
})
```

### 缓存操作

```typescript
// 检查缓存
if (assetManager.has('my-asset')) {
  const asset = assetManager.get('my-asset')
}

// 手动清理缓存
const cleanedCount = assetManager.cleanup()

// 获取缓存统计
const cacheStats = assetManager.getCacheStats()
console.log('缓存使用率:', cacheStats.utilization)
```

## 状态管理

### 监听资产状态

```typescript
import { AssetStateUtils } from '@dl-engine/engine-core/assets'

// 监听资产变化
assetManager.on('assetLoaded', (asset) => {
  console.log('资产加载完成:', asset.name)
})

assetManager.on('assetError', (error, assetId) => {
  console.error('资产加载失败:', assetId, error)
})

// 获取所有资产
const allAssets = AssetStateUtils.getAllAssets()

// 查询特定资产
const textures = assetManager.query({
  types: [AssetType.TEXTURE],
  statuses: [AssetStatus.LOADED]
})
```

### 资产统计

```typescript
// 获取统计信息
const stats = assetManager.getStats()
console.log('总资产数:', stats.totalAssets)
console.log('缓存大小:', stats.cacheSize)
console.log('内存使用:', stats.memoryUsage)
```

## 自定义加载器

```typescript
import { AssetLoader } from '@dl-engine/engine-core/assets'

class CustomLoader extends AssetLoader {
  constructor() {
    super()
    this.supportedTypes = [AssetType.DATA]
  }

  protected async performLoad(asset: Asset, options: AssetLoadOptions): Promise<Asset> {
    const response = await fetch(asset.url)
    const data = await response.json()
    
    return {
      ...asset,
      data,
      size: JSON.stringify(data).length,
      status: AssetStatus.LOADED
    }
  }
}

// 注册自定义加载器
const loaderManager = new LoaderManager()
loaderManager.registerLoader(AssetType.DATA, new CustomLoader())
```

## 最佳实践

### 1. 预加载重要资产

```typescript
// 预加载关键资产
await assetManager.preload('ui-texture', '/ui/button.png', AssetType.TEXTURE)
await assetManager.preload('main-character', '/models/hero.glb', AssetType.MODEL)
```

### 2. 使用优先级

```typescript
// 高优先级加载用户界面资产
await assetManager.load('ui-bg', '/ui/background.jpg', AssetType.TEXTURE, {
  priority: AssetPriority.HIGH
})

// 低优先级加载装饰性资产
await assetManager.load('decoration', '/models/plant.glb', AssetType.MODEL, {
  priority: AssetPriority.LOW
})
```

### 3. 内存管理

```typescript
// 及时卸载不需要的资产
assetManager.unload('temporary-texture')

// 定期清理
setInterval(() => {
  assetManager.cleanup()
}, 60000) // 每分钟清理一次
```

### 4. 错误处理

```typescript
try {
  const asset = await assetManager.load('risky-asset', '/path/to/asset.glb', AssetType.MODEL)
} catch (error) {
  console.error('加载失败，使用默认资产:', error)
  const fallback = await assetManager.load('default-model', '/defaults/cube.glb', AssetType.MODEL)
}
```

## API 参考

详细的 API 文档请参考各个模块的 TypeScript 类型定义。

## 性能优化

1. **合理设置缓存大小**: 根据目标设备的内存容量设置合适的缓存大小
2. **使用压缩格式**: 优先使用 Draco 压缩的 GLTF 和 KTX2 纹理
3. **按需加载**: 避免一次性加载所有资产，实现渐进式加载
4. **监控内存使用**: 定期检查资产统计信息，及时清理不需要的资产
