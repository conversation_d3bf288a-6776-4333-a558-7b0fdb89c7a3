/**
 * DL-Engine 资产系统ECS系统
 * 处理资产加载、管理和更新
 */

import { defineSystem, defineQuery, Entity, getComponent, setComponent, hasComponent } from '@dl-engine/engine-ecs'
import { AssetManager } from '../AssetManager'
import { AssetStatus, AssetType } from '../AssetTypes'
import {
  AssetReferenceComponent,
  AssetLoaderComponent,
  AssetPreloaderComponent,
  AssetDependencyComponent,
  AssetPerformanceComponent,
  AssetComponentUtils
} from '../components/AssetComponents'

/**
 * 资产加载系统
 * 处理实体的资产加载请求
 */
export const AssetLoadingSystem = defineSystem({
  uuid: 'dl-engine.asset-loading-system',
  insert: { before: 'dl-engine.render-system' as any },
  execute: () => {
    const assetManager = AssetManager.getInstance()
    
    // 查询需要自动加载的资产引用
    const autoLoadEntities = defineQuery([AssetReferenceComponent])

    for (const entity of autoLoadEntities) {
      if (!hasComponent(entity, AssetReferenceComponent)) continue
      const assetRef = getComponent(entity, AssetReferenceComponent)

      // 检查是否需要自动加载
      if (assetRef.autoLoad &&
          assetRef.status === AssetStatus.UNLOADED &&
          !assetRef.isLoaded) {

        // 开始加载资产
        loadAssetForEntity(entity, assetRef, assetManager)
      }
    }

    // 查询资产加载器组件
    const loaderEntities = defineQuery([AssetLoaderComponent])

    for (const entity of loaderEntities) {
      if (!hasComponent(entity, AssetLoaderComponent)) continue
      const loader = getComponent(entity, AssetLoaderComponent)
      
      // 处理批量加载
      if (!loader.isLoading && !loader.isComplete && loader.assetsToLoad.length > 0) {
        processBatchLoading(entity, loader, assetManager)
      }
      
      // 更新加载进度
      updateLoadingProgress(entity, loader)
    }
  }
})

/**
 * 资产预加载系统
 * 处理资产预加载逻辑
 */
export const AssetPreloadingSystem = defineSystem({
  uuid: 'dl-engine.asset-preloading-system',
  insert: { before: 'dl-engine.asset-loading-system' as any },
  execute: () => {
    const assetManager = AssetManager.getInstance()
    
    const preloadEntities = defineQuery([AssetPreloaderComponent])

    for (const entity of preloadEntities) {
      if (!hasComponent(entity, AssetPreloaderComponent)) continue
      const preloader = getComponent(entity, AssetPreloaderComponent)
      
      // 检查是否应该触发预加载
      if (!preloader.hasTriggered && shouldTriggerPreload(entity, preloader)) {
        triggerPreload(entity, preloader, assetManager)
      }
      
      // 更新预加载进度
      updatePreloadProgress(entity, preloader)
    }
  }
})

/**
 * 资产依赖系统
 * 处理资产依赖关系
 */
export const AssetDependencySystem = defineSystem({
  uuid: 'dl-engine.asset-dependency-system',
  insert: { before: 'dl-engine.asset-loading-system' as any },
  execute: () => {
    const assetManager = AssetManager.getInstance()
    
    const dependencyEntities = defineQuery([AssetDependencyComponent])

    for (const entity of dependencyEntities) {
      if (!hasComponent(entity, AssetDependencyComponent)) continue
      const dependency = getComponent(entity, AssetDependencyComponent)
      
      // 检查是否需要自动加载依赖
      if (dependency.autoLoadDependencies && !dependency.allDependenciesLoaded) {
        loadDependencies(entity, dependency, assetManager)
      }
      
      // 更新依赖状态
      updateDependencyStatus(entity, dependency, assetManager)
    }
  }
})

/**
 * 资产性能监控系统
 * 监控资产性能和使用情况
 */
export const AssetPerformanceSystem = defineSystem({
  uuid: 'dl-engine.asset-performance-system',
  insert: { after: 'dl-engine.asset-loading-system' as any },
  execute: () => {
    const assetManager = AssetManager.getInstance()
    
    const performanceEntities = defineQuery([AssetPerformanceComponent])

    for (const entity of performanceEntities) {
      if (!hasComponent(entity, AssetPerformanceComponent)) continue
      const performance = getComponent(entity, AssetPerformanceComponent)
      
      // 更新性能指标
      updatePerformanceMetrics(entity, performance, assetManager)
      
      // 检查性能瓶颈
      checkPerformanceBottlenecks(entity, performance)
      
      // 生成优化建议
      generateOptimizationSuggestions(entity, performance)
    }
  }
})

// 辅助函数

/**
 * 为实体加载资产
 */
async function loadAssetForEntity(
  entity: Entity,
  assetRef: any,
  assetManager: AssetManager
): Promise<void> {
  try {
    // 更新状态为加载中
    AssetComponentUtils.updateAssetReferenceStatus(entity, AssetStatus.LOADING, 0)
    
    // 加载资产
    const asset = await assetManager.load(
      assetRef.assetId,
      assetRef.assetUrl,
      assetRef.assetType,
      {
        priority: assetRef.priority,
        onProgress: (progress) => {
          AssetComponentUtils.updateAssetReferenceStatus(entity, AssetStatus.LOADING, progress)
        }
      }
    )
    
    // 更新状态为已加载
    AssetComponentUtils.updateAssetReferenceStatus(entity, AssetStatus.LOADED, 1)
    
    // 根据资产类型创建相应的组件
    createAssetSpecificComponent(entity, asset)
    
  } catch (error) {
    // 更新状态为错误
    const errorMessage = error instanceof Error ? error.message : String(error)
    AssetComponentUtils.updateAssetReferenceStatus(entity, AssetStatus.ERROR, 0, errorMessage)
  }
}

/**
 * 处理批量加载
 */
async function processBatchLoading(
  entity: Entity,
  loader: any,
  assetManager: AssetManager
): Promise<void> {
  try {
    // 标记为正在加载
    const currentLoader = getComponent(entity, AssetLoaderComponent)
    setComponent(entity, AssetLoaderComponent, {
      ...currentLoader,
      isLoading: true,
      loadStartTime: Date.now()
    })

    // 批量加载资产
    const assets = await assetManager.loadBatch(loader.assetsToLoad)

    // 更新加载结果
    const updatedLoader = getComponent(entity, AssetLoaderComponent)
    setComponent(entity, AssetLoaderComponent, {
      ...updatedLoader,
      isLoading: false,
      isComplete: true,
      loadedAssets: assets.map(asset => asset.id),
      loadEndTime: Date.now(),
      overallProgress: 1
    })

  } catch (error) {
    // 处理加载错误
    const errorLoader = getComponent(entity, AssetLoaderComponent)
    setComponent(entity, AssetLoaderComponent, {
      ...errorLoader,
      isLoading: false,
      hasErrors: true,
      loadEndTime: Date.now()
    })
  }
}

/**
 * 更新加载进度
 */
function updateLoadingProgress(entity: Entity, loader: any): void {
  if (!loader.isLoading) return
  
  const totalAssets = loader.assetsToLoad.length
  const loadedAssets = loader.loadedAssets.length
  const failedAssets = loader.failedAssets.length
  
  const progress = totalAssets > 0 ? (loadedAssets + failedAssets) / totalAssets : 0
  
  const currentLoader = getComponent(entity, AssetLoaderComponent)
  setComponent(entity, AssetLoaderComponent, {
    ...currentLoader,
    overallProgress: progress,
    currentlyLoading: Math.min(loader.maxConcurrentLoads, totalAssets - loadedAssets - failedAssets)
  })
}

/**
 * 检查是否应该触发预加载
 */
function shouldTriggerPreload(entity: Entity, preloader: any): boolean {
  switch (preloader.triggerCondition) {
    case 'immediate':
      return true
    
    case 'onVisible':
      // 检查实体是否可见（需要渲染组件）
      return true // 简化实现
    
    case 'onProximity':
      // 检查距离（需要位置组件）
      return true // 简化实现
    
    case 'onDemand':
      return false
    
    default:
      return false
  }
}

/**
 * 触发预加载
 */
async function triggerPreload(
  entity: Entity,
  preloader: any,
  assetManager: AssetManager
): Promise<void> {
  try {
    const currentPreloader = getComponent(entity, AssetPreloaderComponent)
    setComponent(entity, AssetPreloaderComponent, {
      ...currentPreloader,
      hasTriggered: true,
      preloadStartTime: Date.now()
    })

    // 预加载资产
    for (const assetInfo of preloader.preloadAssets) {
      await assetManager.preload(
        assetInfo.id,
        assetInfo.url,
        assetInfo.type,
        { priority: assetInfo.priority }
      )
    }

    const updatedPreloader = getComponent(entity, AssetPreloaderComponent)
    setComponent(entity, AssetPreloaderComponent, {
      ...updatedPreloader,
      isPreloadComplete: true,
      preloadProgress: 1,
      preloadEndTime: Date.now()
    })
    
  } catch (error) {
    console.warn('预加载失败:', error)
  }
}

/**
 * 更新预加载进度
 */
function updatePreloadProgress(entity: Entity, preloader: any): void {
  if (!preloader.hasTriggered || preloader.isPreloadComplete) return
  
  // 简化的进度计算
  const elapsed = Date.now() - preloader.preloadStartTime
  const estimatedTotal = preloader.preloadAssets.length * 1000 // 假设每个资产1秒
  const progress = Math.min(elapsed / estimatedTotal, 0.99)
  
  const currentPreloader = getComponent(entity, AssetPreloaderComponent)
  setComponent(entity, AssetPreloaderComponent, {
    ...currentPreloader,
    preloadProgress: progress
  })
}

/**
 * 加载依赖资产
 */
async function loadDependencies(
  entity: Entity,
  dependency: any,
  assetManager: AssetManager
): Promise<void> {
  try {
    for (const depId of dependency.dependencies) {
      if (!assetManager.has(depId)) {
        // 这里需要从某处获取依赖资产的URL和类型
        // 简化实现，假设已知
        console.log(`加载依赖资产: ${depId}`)
      }
    }
  } catch (error) {
    console.warn('依赖加载失败:', error)
  }
}

/**
 * 更新依赖状态
 */
function updateDependencyStatus(
  entity: Entity,
  dependency: any,
  assetManager: AssetManager
): void {
  let allLoaded = true
  let totalProgress = 0
  
  for (const depId of dependency.dependencies) {
    const asset = assetManager.get(depId)
    if (asset) {
      totalProgress += asset.progress
      if (asset.status !== AssetStatus.LOADED) {
        allLoaded = false
      }
    } else {
      allLoaded = false
    }
  }
  
  const progress = dependency.dependencies.length > 0 ? 
    totalProgress / dependency.dependencies.length : 0
  
  const currentDependency = getComponent(entity, AssetDependencyComponent)
  setComponent(entity, AssetDependencyComponent, {
    ...currentDependency,
    allDependenciesLoaded: allLoaded,
    dependencyProgress: progress
  })
}

/**
 * 更新性能指标
 */
function updatePerformanceMetrics(
  entity: Entity,
  performance: any,
  assetManager: AssetManager
): void {
  const asset = assetManager.get(performance.assetId)
  if (!asset) return
  
  const now = Date.now()
  const timeSinceLastAccess = now - performance.lastAccessTime
  
  // 更新访问统计
  const currentPerformance = getComponent(entity, AssetPerformanceComponent)
  setComponent(entity, AssetPerformanceComponent, {
    ...currentPerformance,
    lastAccessTime: now,
    accessCount: performance.accessCount + 1,
    averageAccessInterval: (performance.averageAccessInterval + timeSinceLastAccess) / 2
  })
}

/**
 * 检查性能瓶颈
 */
function checkPerformanceBottlenecks(entity: Entity, performance: any): void {
  const isBottleneck = 
    performance.loadTime > 5000 || // 加载时间超过5秒
    performance.fileSize > 50 * 1024 * 1024 || // 文件大小超过50MB
    performance.memoryUsage > 100 * 1024 * 1024 // 内存使用超过100MB
  
  const currentPerformance = getComponent(entity, AssetPerformanceComponent)
  setComponent(entity, AssetPerformanceComponent, {
    ...currentPerformance,
    isBottleneck,
    performanceScore: calculatePerformanceScore(performance)
  })
}

/**
 * 计算性能评分
 */
function calculatePerformanceScore(performance: any): number {
  let score = 100
  
  // 根据加载时间扣分
  if (performance.loadTime > 1000) {
    score -= Math.min(50, (performance.loadTime - 1000) / 100)
  }
  
  // 根据文件大小扣分
  if (performance.fileSize > 10 * 1024 * 1024) {
    score -= Math.min(30, (performance.fileSize - 10 * 1024 * 1024) / (1024 * 1024))
  }
  
  // 根据内存使用扣分
  if (performance.memoryUsage > 50 * 1024 * 1024) {
    score -= Math.min(20, (performance.memoryUsage - 50 * 1024 * 1024) / (1024 * 1024))
  }
  
  return Math.max(0, score)
}

/**
 * 生成优化建议
 */
function generateOptimizationSuggestions(entity: Entity, performance: any): void {
  const suggestions: string[] = []
  
  if (performance.loadTime > 3000) {
    suggestions.push('考虑压缩资产或使用更快的CDN')
  }
  
  if (performance.fileSize > 20 * 1024 * 1024) {
    suggestions.push('考虑降低资产质量或分割为多个文件')
  }
  
  if (performance.accessCount < 5 && performance.memoryUsage > 10 * 1024 * 1024) {
    suggestions.push('考虑延迟加载或从缓存中移除')
  }
  
  const currentPerformance = getComponent(entity, AssetPerformanceComponent)
  setComponent(entity, AssetPerformanceComponent, {
    ...currentPerformance,
    optimizationSuggestions: suggestions
  })
}

/**
 * 根据资产类型创建相应组件
 */
function createAssetSpecificComponent(entity: Entity, asset: any): void {
  // 这里可以根据资产类型创建相应的组件
  // 例如：ModelAssetComponent, TextureAssetComponent, AudioAssetComponent
  console.log(`为实体 ${entity} 创建 ${asset.type} 资产组件`)
}
