/**
 * DL-Engine 物理引擎
 * 基于Rapier3D的高性能物理模拟
 * 支持碰撞检测、空间查询、约束系统
 */

// 导出物理世界
export * from './src/PhysicsWorld'

// 导出物理组件
export * from './src/components'

// 导出碰撞检测 (避免与空间查询的 raycast 冲突)
export {
  CollisionDetector
} from './src/collision'

// 导出空间查询
export * from './src/spatial'

// 导出约束系统
export * from './src/constraints'

// 导出空间查询系统
export * from './src/spatial'

// 导出物理类型 (显式导出以避免冲突)
export {
  PhysicsVector3,
  PhysicsQuaternion,
  BodyType,
  ColliderShape,
  ConstraintType as PhysicsConstraintType,
  ConstraintParams,
  RaycastHit as PhysicsRaycastHit
} from './src/types'

// TODO: 待实现的模块
// export * from './src/systems'
// export * from './src/utils'
// export * from './src/PhysicsState'
