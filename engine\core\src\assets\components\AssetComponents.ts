/**
 * DL-Engine 资产系统ECS组件
 * 将资产系统与ECS架构集成
 */

import { defineComponent, Entity, setComponent, getComponent, hasComponent } from '@dl-engine/engine-ecs'
import { AssetType, AssetStatus, AssetPriority } from '../AssetTypes'

/**
 * 资产引用组件
 * 用于实体引用资产
 */
export const AssetReferenceComponent = defineComponent({
  name: 'AssetReference',
  schema: {
    /** 资产ID */
    assetId: '',

    /** 资产类型 */
    assetType: '',

    /** 资产URL */
    assetUrl: '',

    /** 加载状态 */
    status: '',

    /** 加载优先级 */
    priority: 0,

    /** 是否自动加载 */
    autoLoad: true,

    /** 是否已加载 */
    isLoaded: false,

    /** 加载进度 (0-1) */
    progress: 0,

    /** 错误信息 */
    error: '',

    /** 最后更新时间 */
    lastUpdated: 0
  }
})

/**
 * 模型资产组件
 * 用于3D模型资产
 */
export const ModelAssetComponent = defineComponent({
  name: 'ModelAsset',
  schema: {
    /** 资产引用 */
    assetId: 'string',
    
    /** 模型场景对象 */
    scene: 'object' as any,
    
    /** 动画列表 */
    animations: 'object' as any,
    
    /** 当前播放的动画索引 */
    currentAnimationIndex: 'number',
    
    /** 是否自动播放动画 */
    autoPlayAnimation: 'boolean',
    
    /** 动画播放速度 */
    animationSpeed: 'number',
    
    /** 是否循环播放 */
    loopAnimation: 'boolean',
    
    /** 模型缩放 */
    scale: 'object' as any, // Vector3
    
    /** 模型旋转 */
    rotation: 'object' as any, // Euler
    
    /** 模型位置偏移 */
    positionOffset: 'object' as any, // Vector3
    
    /** 是否可见 */
    visible: 'boolean',
    
    /** 材质覆盖 */
    materialOverrides: 'object' as any
  }
})

/**
 * 纹理资产组件
 * 用于纹理资产
 */
export const TextureAssetComponent = defineComponent({
  name: 'TextureAsset',
  schema: {
    /** 资产引用 */
    assetId: 'string',

    /** 资产类型 */
    assetType: 'string',

    /** 资产URL */
    assetUrl: 'string',

    /** 资产状态 */
    status: 'string',

    /** 资产优先级 */
    priority: 'number',

    /** 纹理对象 */
    texture: 'object' as any,

    /** 纹理类型 */
    textureType: 'string', // 'diffuse', 'normal', 'roughness', 'metalness', etc.

    /** 纹理重复 */
    repeat: 'object' as any, // Vector2

    /** 纹理偏移 */
    offset: 'object' as any, // Vector2

    /** 纹理旋转 */
    rotation: 'number',

    /** 是否翻转Y轴 */
    flipY: 'boolean',

    /** 包装模式 */
    wrapS: 'number',
    wrapT: 'number',

    /** 过滤模式 */
    minFilter: 'number',
    magFilter: 'number',

    /** 是否生成Mipmap */
    generateMipmaps: 'boolean',

    /** 纹理颜色空间 */
    colorSpace: 'string'
  }
})

/**
 * 音频资产组件
 * 用于音频资产
 */
export const AudioAssetComponent = defineComponent({
  name: 'AudioAsset',
  schema: {
    /** 资产引用 */
    assetId: 'string',
    
    /** 音频缓冲区 */
    audioBuffer: 'object' as any,
    
    /** 音频源节点 */
    sourceNode: 'object' as any,
    
    /** 增益节点 */
    gainNode: 'object' as any,
    
    /** 音量 (0-1) */
    volume: 'number',
    
    /** 是否循环 */
    loop: 'boolean',
    
    /** 是否自动播放 */
    autoPlay: 'boolean',
    
    /** 是否正在播放 */
    isPlaying: 'boolean',
    
    /** 是否暂停 */
    isPaused: 'boolean',
    
    /** 播放位置 (秒) */
    currentTime: 'number',
    
    /** 音频时长 (秒) */
    duration: 'number',
    
    /** 播放速度 */
    playbackRate: 'number',
    
    /** 是否3D音频 */
    is3D: 'boolean',
    
    /** 3D音频参数 */
    spatialParams: 'object' as any
  }
})

/**
 * 资产加载器组件
 * 用于管理实体的资产加载
 */
export const AssetLoaderComponent = defineComponent({
  name: 'AssetLoader',
  schema: {
    /** 待加载的资产列表 */
    assetsToLoad: [] as any[],

    /** 已加载的资产列表 */
    loadedAssets: [] as string[],

    /** 加载失败的资产列表 */
    failedAssets: [] as any[],

    /** 总体加载进度 (0-1) */
    overallProgress: 0,

    /** 是否正在加载 */
    isLoading: false,

    /** 是否加载完成 */
    isComplete: false,

    /** 是否有错误 */
    hasErrors: false,

    /** 加载开始时间 */
    loadStartTime: 0,

    /** 加载结束时间 */
    loadEndTime: 0,

    /** 最大并发加载数 */
    maxConcurrentLoads: 3,

    /** 当前正在加载的资产数 */
    currentlyLoading: 0
  }
})

/**
 * 资产缓存组件
 * 用于管理实体级别的资产缓存
 */
export const AssetCacheComponent = defineComponent({
  name: 'AssetCache',
  schema: {
    /** 缓存的资产ID列表 */
    cachedAssets: 'object' as any, // Array<string>
    
    /** 缓存大小限制 (字节) */
    maxCacheSize: 'number',
    
    /** 当前缓存大小 (字节) */
    currentCacheSize: 'number',
    
    /** 缓存策略 */
    cacheStrategy: 'string',
    
    /** 缓存TTL (毫秒) */
    cacheTTL: 'number',
    
    /** 是否启用自动清理 */
    autoCleanup: 'boolean',
    
    /** 上次清理时间 */
    lastCleanupTime: 'number',
    
    /** 清理间隔 (毫秒) */
    cleanupInterval: 'number',
    
    /** 缓存命中次数 */
    cacheHits: 'number',
    
    /** 缓存未命中次数 */
    cacheMisses: 'number'
  }
})

/**
 * 资产预加载组件
 * 用于预加载资产
 */
export const AssetPreloaderComponent = defineComponent({
  name: 'AssetPreloader',
  schema: {
    /** 预加载资产列表 */
    preloadAssets: [] as any[],

    /** 预加载触发条件 */
    triggerCondition: 'immediate',

    /** 触发距离 (用于proximity触发) */
    triggerDistance: 10,

    /** 是否已触发预加载 */
    hasTriggered: false,

    /** 预加载进度 (0-1) */
    preloadProgress: 0,

    /** 是否预加载完成 */
    isPreloadComplete: false,

    /** 预加载开始时间 */
    preloadStartTime: 0,

    /** 预加载结束时间 */
    preloadEndTime: 0
  }
})

/**
 * 资产依赖组件
 * 用于管理资产之间的依赖关系
 */
export const AssetDependencyComponent = defineComponent({
  name: 'AssetDependency',
  schema: {
    /** 主资产ID */
    primaryAssetId: '',

    /** 依赖的资产ID列表 */
    dependencies: [] as string[],

    /** 依赖加载状态 */
    dependencyStatus: {},

    /** 是否所有依赖都已加载 */
    allDependenciesLoaded: false,

    /** 依赖加载进度 (0-1) */
    dependencyProgress: 0,

    /** 是否自动加载依赖 */
    autoLoadDependencies: true,

    /** 依赖加载优先级 */
    dependencyPriority: AssetPriority.NORMAL
  }
})

/**
 * 资产性能监控组件
 * 用于监控资产加载和使用性能
 */
export const AssetPerformanceComponent = defineComponent({
  name: 'AssetPerformance',
  schema: {
    /** 监控的资产ID */
    assetId: '',

    /** 加载时间 (毫秒) */
    loadTime: 0,

    /** 文件大小 (字节) */
    fileSize: 0,

    /** 内存使用 (字节) */
    memoryUsage: 0,

    /** 访问次数 */
    accessCount: 0,

    /** 最后访问时间 */
    lastAccessTime: 0,

    /** 平均访问间隔 (毫秒) */
    averageAccessInterval: 0,

    /** 是否为性能瓶颈 */
    isBottleneck: false,

    /** 性能评分 (0-100) */
    performanceScore: 100,

    /** 优化建议 */
    optimizationSuggestions: [] as string[]
  }
})

/**
 * 资产组件工具函数
 */
export const AssetComponentUtils = {
  /**
   * 创建资产引用
   */
  createAssetReference: (
    entity: Entity,
    assetId: string,
    assetUrl: string,
    assetType: AssetType,
    options: {
      priority?: AssetPriority
      autoLoad?: boolean
    } = {}
  ) => {
    setComponent(entity, AssetReferenceComponent, {
      assetId,
      assetType: assetType,
      assetUrl,
      status: AssetStatus.UNLOADED,
      priority: options.priority || AssetPriority.NORMAL,
      autoLoad: options.autoLoad !== false,
      isLoaded: false,
      progress: 0,
      error: '',
      lastUpdated: Date.now()
    })
  },

  /**
   * 更新资产引用状态
   */
  updateAssetReferenceStatus: (
    entity: Entity,
    status: AssetStatus,
    progress?: number,
    error?: string
  ) => {
    if (hasComponent(entity, AssetReferenceComponent)) {
      const component = getComponent(entity, AssetReferenceComponent)
      setComponent(entity, AssetReferenceComponent, {
        ...component,
        status: status,
        progress: progress !== undefined ? progress : component.progress,
        error: error || component.error,
        isLoaded: status === AssetStatus.LOADED,
        lastUpdated: Date.now()
      })
    }
  },

  /**
   * 检查实体是否有指定类型的资产
   */
  hasAssetOfType: (entity: Entity, assetType: AssetType): boolean => {
    if (!hasComponent(entity, AssetReferenceComponent)) return false
    const component = getComponent(entity, AssetReferenceComponent)
    return component.assetType === assetType
  },

  /**
   * 获取实体的资产加载进度
   */
  getAssetLoadProgress: (entity: Entity): number => {
    if (!hasComponent(entity, AssetReferenceComponent)) return 0
    const component = getComponent(entity, AssetReferenceComponent)
    return component.progress
  },

  /**
   * 检查实体的资产是否已加载
   */
  isAssetLoaded: (entity: Entity): boolean => {
    if (!hasComponent(entity, AssetReferenceComponent)) return false
    const component = getComponent(entity, AssetReferenceComponent)
    return component.isLoaded
  }
}
