/**
 * DL-Engine 引擎状态管理
 * 管理引擎级别的全局状态
 */

import { defineState, getState, State } from '@dl-engine/engine-state'
import { UserID } from '@dl-engine/shared-common'

/**
 * 引擎运行模式
 */
export enum EngineMode {
  /** 开发模式 */
  DEVELOPMENT = 'development',
  /** 生产模式 */
  PRODUCTION = 'production',
  /** 测试模式 */
  TEST = 'test',
  /** 编辑器模式 */
  EDITOR = 'editor'
}

/**
 * 引擎平台类型
 */
export enum EnginePlatform {
  /** 桌面浏览器 */
  DESKTOP = 'desktop',
  /** 移动浏览器 */
  MOBILE = 'mobile',
  /** VR设备 */
  VR = 'vr',
  /** AR设备 */
  AR = 'ar',
  /** Node.js服务器 */
  SERVER = 'server'
}

/**
 * 引擎状态接口
 */
export interface EngineStateType {
  /** 引擎是否已初始化 */
  isInitialized: boolean
  
  /** 引擎是否正在运行 */
  isRunning: boolean
  
  /** 引擎运行模式 */
  mode: EngineMode
  
  /** 引擎平台 */
  platform: EnginePlatform
  
  /** 当前用户ID */
  userID: UserID
  
  /** 引擎版本 */
  version: string
  
  /** 引擎名称 */
  name: string
  
  /** 启动时间 */
  startTime: number
  
  /** 运行时间（毫秒） */
  uptime: number
  
  /** 是否启用调试模式 */
  debugMode: boolean
  
  /** 是否启用详细日志 */
  verboseLogging: boolean
  
  /** 当前场景ID */
  currentSceneID: string | null
  
  /** 网络连接状态 */
  networkState: {
    isConnected: boolean
    connectionType: 'offline' | 'websocket' | 'webrtc'
    latency: number
    bandwidth: number
  }
  
  /** 渲染状态 */
  renderState: {
    isRendering: boolean
    renderMode: '2d' | '3d' | 'vr' | 'ar'
    devicePixelRatio: number
    canvasSize: { width: number; height: number }
  }
  
  /** 输入状态 */
  inputState: {
    hasKeyboard: boolean
    hasMouse: boolean
    hasTouch: boolean
    hasGamepad: boolean
    hasVRControllers: boolean
  }
  
  /** 功能特性支持 */
  features: {
    webgl: boolean
    webgl2: boolean
    webxr: boolean
    webrtc: boolean
    webassembly: boolean
    sharedArrayBuffer: boolean
    offscreenCanvas: boolean
  }
  
  /** 性能配置 */
  performance: {
    maxEntities: number
    maxComponents: number
    targetFPS: number
    enableProfiling: boolean
    enableMetrics: boolean
  }
  
  /** 教育模式配置 */
  education: {
    isEducationMode: boolean
    courseID: string | null
    studentID: string | null
    teacherID: string | null
    classroomID: string | null
  }
}

/**
 * 检测当前平台
 */
function detectPlatform(): EnginePlatform {
  if (typeof window === 'undefined') {
    return EnginePlatform.SERVER
  }
  
  if (navigator.userAgent.includes('Mobile') || navigator.userAgent.includes('Android')) {
    return EnginePlatform.MOBILE
  }
  
  // 检测VR/AR支持
  if ('xr' in navigator) {
    return EnginePlatform.VR // 默认VR，具体检测需要异步
  }
  
  return EnginePlatform.DESKTOP
}

/**
 * 检测功能特性支持
 */
function detectFeatures() {
  if (typeof window === 'undefined') {
    return {
      webgl: false,
      webgl2: false,
      webxr: false,
      webrtc: false,
      webassembly: false,
      sharedArrayBuffer: false,
      offscreenCanvas: false
    }
  }
  
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl')
  const gl2 = canvas.getContext('webgl2')
  
  return {
    webgl: !!gl,
    webgl2: !!gl2,
    webxr: 'xr' in navigator,
    webrtc: 'RTCPeerConnection' in window,
    webassembly: 'WebAssembly' in window,
    sharedArrayBuffer: 'SharedArrayBuffer' in window,
    offscreenCanvas: 'OffscreenCanvas' in window
  }
}

/**
 * 检测输入设备
 */
function detectInputDevices() {
  if (typeof window === 'undefined') {
    return {
      hasKeyboard: false,
      hasMouse: false,
      hasTouch: false,
      hasGamepad: false,
      hasVRControllers: false
    }
  }
  
  return {
    hasKeyboard: true, // 假设桌面设备有键盘
    hasMouse: !('ontouchstart' in window),
    hasTouch: 'ontouchstart' in window,
    hasGamepad: 'getGamepads' in navigator,
    hasVRControllers: false // 需要异步检测
  }
}

/**
 * 引擎状态默认值
 */
const EngineStateDefaults: EngineStateType = {
  isInitialized: false,
  isRunning: false,
  mode: EngineMode.DEVELOPMENT,
  platform: detectPlatform(),
  userID: '' as UserID,
  version: '1.0.0',
  name: 'Digital Learning Engine',
  startTime: 0,
  uptime: 0,
  debugMode: false,
  verboseLogging: false,
  currentSceneID: null,
  networkState: {
    isConnected: false,
    connectionType: 'offline',
    latency: 0,
    bandwidth: 0
  },
  renderState: {
    isRendering: false,
    renderMode: '3d',
    devicePixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio : 1,
    canvasSize: { width: 1920, height: 1080 }
  },
  inputState: detectInputDevices(),
  features: detectFeatures(),
  performance: {
    maxEntities: 100000,
    maxComponents: 64,
    targetFPS: 60,
    enableProfiling: false,
    enableMetrics: true
  },
  education: {
    isEducationMode: false,
    courseID: null,
    studentID: null,
    teacherID: null,
    classroomID: null
  }
}

/**
 * 引擎状态定义
 */
export const EngineState = defineState({
  name: 'DLEngine.Engine',
  initial: () => EngineStateDefaults
})

/**
 * 引擎状态工具函数
 */
export const EngineStateUtils = {
  /**
   * 获取引擎信息
   */
  getEngineInfo: () => ({
    name: 'DL-Engine',
    version: getState(EngineState).version,
    mode: getState(EngineState).mode,
    platform: getState(EngineState).platform,
    uptime: getState(EngineState).uptime,
    isInitialized: getState(EngineState).isInitialized,
    isRunning: getState(EngineState).isRunning
  }),
  
  /**
   * 检查是否为开发模式
   */
  isDevelopment: (): boolean => {
    return getState(EngineState).mode === EngineMode.DEVELOPMENT
  },
  
  /**
   * 检查是否为生产模式
   */
  isProduction: (): boolean => {
    return getState(EngineState).mode === EngineMode.PRODUCTION
  },
  
  /**
   * 检查是否为教育模式
   */
  isEducationMode: (): boolean => {
    return getState(EngineState).education.isEducationMode
  },
  
  /**
   * 格式化运行时间
   */
  formatUptime: (): string => {
    const uptime = getState(EngineState).uptime
    const seconds = Math.floor(uptime / 1000) % 60
    const minutes = Math.floor(uptime / (1000 * 60)) % 60
    const hours = Math.floor(uptime / (1000 * 60 * 60))
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
}
