/**
 * DL-Engine 推荐引擎核心实现
 * 提供多种推荐算法的统一接口
 */

import { Entity } from '@dl-engine/engine-ecs'

/**
 * 推荐项目接口
 */
export interface RecommendationItem {
  /** 项目ID */
  id: string

  /** 项目ID（别名，用于兼容性） */
  itemId: string

  /** 项目类型 */
  type: 'course' | 'lesson' | 'exercise' | 'resource' | 'path' | 'content'

  /** 项目标题 */
  title: string

  /** 项目描述 */
  description?: string

  /** 推荐分数 (0-1) */
  score: number

  /** 推荐理由 */
  reason: string

  /** 推荐算法 */
  algorithm: RecommendationAlgorithm

  /** 置信度 (0-1) */
  confidence: number

  /** 元数据 */
  metadata?: Record<string, any>

  /** 生成时间 */
  generatedAt: Date
}

/**
 * 推荐算法类型
 */
export type RecommendationAlgorithm = 
  | 'collaborative_filtering'  // 协同过滤
  | 'content_based'           // 基于内容
  | 'hybrid'                  // 混合推荐
  | 'popularity'              // 热门推荐
  | 'knowledge_based'         // 基于知识
  | 'deep_learning'           // 深度学习
  | 'manual'                  // 人工推荐

/**
 * 推荐上下文
 */
export interface RecommendationContext {
  /** 用户ID */
  userId: Entity
  
  /** 当前学习内容 */
  currentContent?: string
  
  /** 学习目标 */
  learningGoals?: string[]
  
  /** 时间限制 */
  timeConstraint?: number
  
  /** 难度偏好 */
  difficultyPreference?: 'easy' | 'medium' | 'hard' | 'adaptive'
  
  /** 内容类型偏好 */
  contentTypePreference?: string[]
  
  /** 排除项目 */
  excludeItems?: string[]
  
  /** 额外参数 */
  additionalParams?: Record<string, any>

  /** 推荐数量限制 */
  limit?: number
}

/**
 * 推荐配置
 */
export interface RecommendationConfig {
  /** 推荐数量限制 */
  limit: number
  
  /** 启用的算法 */
  enabledAlgorithms: RecommendationAlgorithm[]
  
  /** 算法权重 */
  algorithmWeights: Record<RecommendationAlgorithm, number>
  
  /** 最小置信度阈值 */
  minConfidence: number
  
  /** 多样性因子 */
  diversityFactor: number
  
  /** 是否包含解释 */
  includeExplanation: boolean
  
  /** 缓存时间（秒） */
  cacheTimeout: number
}

/**
 * 推荐结果
 */
export interface RecommendationResult {
  /** 推荐项目列表 */
  items: RecommendationItem[]
  
  /** 总数 */
  total: number
  
  /** 算法使用情况 */
  algorithmUsage: Record<RecommendationAlgorithm, number>
  
  /** 生成时间 */
  generatedAt: Date
  
  /** 处理时间（毫秒） */
  processingTime: number
  
  /** 缓存状态 */
  fromCache: boolean
}

/**
 * 推荐引擎抽象基类
 */
export abstract class BaseRecommendationEngine {
  protected config: RecommendationConfig
  
  constructor(config: Partial<RecommendationConfig> = {}) {
    this.config = {
      limit: 10,
      enabledAlgorithms: ['hybrid'],
      algorithmWeights: {
        collaborative_filtering: 0.3,
        content_based: 0.3,
        hybrid: 0.4,
        popularity: 0.2,
        knowledge_based: 0.2,
        deep_learning: 0.3,
        manual: 0.1
      },
      minConfidence: 0.1,
      diversityFactor: 0.3,
      includeExplanation: true,
      cacheTimeout: 300,
      ...config
    }
  }
  
  /**
   * 生成推荐
   */
  abstract generateRecommendations(
    context: RecommendationContext
  ): Promise<RecommendationResult>
  
  /**
   * 更新用户反馈
   */
  abstract updateFeedback(
    userId: Entity,
    itemId: string,
    feedback: 'like' | 'dislike' | 'view' | 'click' | 'complete'
  ): Promise<void>
  
  /**
   * 获取推荐解释
   */
  abstract explainRecommendation(
    userId: Entity,
    itemId: string
  ): Promise<string>
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<RecommendationConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
  
  /**
   * 获取配置
   */
  getConfig(): RecommendationConfig {
    return { ...this.config }
  }
}

/**
 * 默认推荐引擎实现
 */
export class RecommendationEngine extends BaseRecommendationEngine {
  private cache = new Map<string, { result: RecommendationResult; timestamp: number }>()
  private userFeedback = new Map<string, Map<string, string[]>>()
  
  /**
   * 生成推荐
   */
  async generateRecommendations(
    context: RecommendationContext
  ): Promise<RecommendationResult> {
    const startTime = Date.now()
    
    // 检查缓存
    const cacheKey = this.generateCacheKey(context)
    const cached = this.getCachedResult(cacheKey)
    if (cached) {
      return {
        ...cached,
        fromCache: true,
        processingTime: Date.now() - startTime
      }
    }
    
    // 生成推荐
    const recommendations: RecommendationItem[] = []
    const algorithmUsage: Record<RecommendationAlgorithm, number> = {} as any
    
    // 根据启用的算法生成推荐
    for (const algorithm of this.config.enabledAlgorithms) {
      const items = await this.generateByAlgorithm(algorithm, context)
      recommendations.push(...items)
      algorithmUsage[algorithm] = items.length
    }
    
    // 合并和排序推荐
    const mergedRecommendations = this.mergeAndRankRecommendations(recommendations, context)
    
    // 应用多样性过滤
    const diversifiedRecommendations = this.applyDiversityFilter(
      mergedRecommendations,
      this.config.diversityFactor
    )
    
    // 限制数量
    const finalRecommendations = diversifiedRecommendations.slice(0, this.config.limit)
    
    const result: RecommendationResult = {
      items: finalRecommendations,
      total: finalRecommendations.length,
      algorithmUsage,
      generatedAt: new Date(),
      processingTime: Date.now() - startTime,
      fromCache: false
    }
    
    // 缓存结果
    this.cacheResult(cacheKey, result)
    
    return result
  }
  
  /**
   * 根据算法生成推荐
   */
  private async generateByAlgorithm(
    algorithm: RecommendationAlgorithm,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    switch (algorithm) {
      case 'collaborative_filtering':
        return this.generateCollaborativeFiltering(context)
      case 'content_based':
        return this.generateContentBased(context)
      case 'hybrid':
        return this.generateHybrid(context)
      case 'popularity':
        return this.generatePopularity(context)
      case 'knowledge_based':
        return this.generateKnowledgeBased(context)
      case 'deep_learning':
        return this.generateDeepLearning(context)
      case 'manual':
        return this.generateManual(context)
      default:
        return []
    }
  }
  
  /**
   * 协同过滤推荐
   */
  private async generateCollaborativeFiltering(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { userId, limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取用户历史行为数据
      const userHistory = await this.getUserHistory(userId)
      if (userHistory.length === 0) {
        return recommendations
      }

      // 获取相似用户
      const similarUsers = await this.findSimilarUsers(userId, userHistory)
      if (similarUsers.length === 0) {
        return recommendations
      }

      // 基于相似用户的行为推荐内容
      const itemScores = new Map<string, number>()

      for (const similarUser of similarUsers) {
        const similarUserHistory = await this.getUserHistory(similarUser.userId)

        for (const item of similarUserHistory) {
          // 跳过用户已经交互过的内容
          if (userHistory.some(h => h.itemId === item.itemId)) {
            continue
          }

          // 计算推荐分数：相似度 × 用户对该内容的评分
          const score = similarUser.similarity * (item.rating || 1)
          const currentScore = itemScores.get(item.itemId) || 0
          itemScores.set(item.itemId, currentScore + score)
        }
      }

      // 排序并返回推荐结果
      const sortedItems = Array.from(itemScores.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)

      for (const [itemId, score] of sortedItems) {
        const itemInfo = await this.getItemInfo(itemId)
        if (itemInfo) {
          recommendations.push({
            id: itemId,
            itemId,
            type: 'content',
            title: itemInfo.title,
            description: itemInfo.description,
            score,
            reason: '基于相似用户的偏好推荐',
            algorithm: 'collaborative_filtering',
            confidence: score,
            generatedAt: new Date(),
            metadata: {
              algorithm: 'collaborative_filtering',
              similarUserCount: similarUsers.length,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
    } catch (error) {
      console.error('协同过滤推荐失败:', error)
      return []
    }
  }
  
  /**
   * 基于内容的推荐
   */
  private async generateContentBased(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { userId, currentContent, limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取用户偏好特征
      const userPreferences = await this.getUserPreferences(userId)

      // 获取当前内容的特征（如果有）
      let contentFeatures: ContentFeatures | null = null
      if (currentContent) {
        contentFeatures = await this.getContentFeatures(currentContent)
      }

      // 获取候选内容列表
      const candidateItems = await this.getCandidateItems(context)

      // 计算内容相似度分数
      const itemScores: Array<{itemId: string, score: number}> = []

      for (const item of candidateItems) {
        const itemFeatures = await this.getContentFeatures(item.id)
        if (!itemFeatures) continue

        let score = 0

        // 基于用户偏好计算分数
        if (userPreferences) {
          score += this.calculatePreferenceScore(userPreferences, itemFeatures) * 0.6
        }

        // 基于当前内容相似度计算分数
        if (contentFeatures) {
          score += this.calculateContentSimilarity(contentFeatures, itemFeatures) * 0.4
        }

        if (score > 0) {
          itemScores.push({ itemId: item.id, score })
        }
      }

      // 排序并返回推荐结果
      const sortedItems = itemScores
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

      for (const { itemId, score } of sortedItems) {
        const itemInfo = await this.getItemInfo(itemId)
        if (itemInfo) {
          recommendations.push({
            id: itemId,
            itemId,
            type: 'content',
            title: itemInfo.title,
            description: itemInfo.description,
            score,
            reason: '基于内容特征相似性推荐',
            algorithm: 'content_based',
            confidence: score,
            generatedAt: new Date(),
            metadata: {
              algorithm: 'content_based',
              contentSimilarity: score,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
    } catch (error) {
      console.error('基于内容的推荐失败:', error)
      return []
    }
  }
  
  /**
   * 混合推荐
   */
  private async generateHybrid(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    // 结合多种算法的推荐结果
    const collaborative = await this.generateCollaborativeFiltering(context)
    const contentBased = await this.generateContentBased(context)
    const popularity = await this.generatePopularity(context)
    
    return [...collaborative, ...contentBased, ...popularity]
  }
  
  /**
   * 热门推荐
   */
  private async generatePopularity(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取热门内容数据
      const popularItems = await this.getPopularItems(limit * 2) // 获取更多候选项

      // 根据热门度排序和过滤
      const filteredItems = popularItems
        .filter(item => {
          // 过滤掉用户已经交互过的内容
          if (context.excludeItems?.includes(item.id)) {
            return false
          }

          // 根据用户偏好过滤
          if (context.contentTypePreference) {
            return context.contentTypePreference.includes(item.type)
          }

          return true
        })
        .slice(0, limit)

      // 构建推荐结果
      for (const item of filteredItems) {
        const itemInfo = await this.getItemInfo(item.id)
        if (itemInfo) {
          recommendations.push({
            id: item.id,
            itemId: item.id,
            type: item.type as any,
            title: itemInfo.title,
            description: itemInfo.description,
            score: item.popularityScore,
            reason: `热门内容推荐 (热度: ${item.viewCount} 次浏览)`,
            algorithm: 'popularity',
            confidence: 0.8,
            generatedAt: new Date(),
            metadata: {
              viewCount: item.viewCount,
              rating: item.averageRating,
              popularityScore: item.popularityScore,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
    } catch (error) {
      console.error('热门推荐失败:', error)
      return []
    }
  }
  
  /**
   * 基于知识的推荐
   */
  private async generateKnowledgeBased(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { userId, learningGoals, difficultyPreference, limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取用户当前知识水平
      const userKnowledge = await this.getUserKnowledgeLevel(userId)

      // 获取学习路径规则
      const learningRules = await this.getLearningRules()

      // 根据知识图谱推荐
      const candidateItems = await this.getCandidateItems(context)

      for (const item of candidateItems) {
        const itemInfo = await this.getItemInfo(item.id)
        const itemFeatures = await this.getContentFeatures(item.id)

        if (!itemInfo || !itemFeatures) continue

        let score = 0
        let reason = ''

        // 基于学习目标匹配
        if (learningGoals && learningGoals.length > 0) {
          const goalMatch = this.calculateGoalMatch(learningGoals, itemFeatures)
          score += goalMatch * 0.4
          if (goalMatch > 0.5) {
            reason += '符合学习目标; '
          }
        }

        // 基于难度适配
        if (difficultyPreference && userKnowledge) {
          const difficultyMatch = this.calculateDifficultyMatch(
            difficultyPreference,
            itemFeatures.difficulty,
            userKnowledge.level
          )
          score += difficultyMatch * 0.3
          if (difficultyMatch > 0.5) {
            reason += '难度适中; '
          }
        }

        // 基于前置知识检查
        const prerequisiteMatch = this.checkPrerequisites(userKnowledge, itemFeatures)
        score += prerequisiteMatch * 0.3
        if (prerequisiteMatch > 0.8) {
          reason += '满足前置条件; '
        }

        if (score > 0.3) { // 只推荐分数较高的内容
          recommendations.push({
            id: item.id,
            itemId: item.id,
            type: item.type as any,
            title: itemInfo.title,
            description: itemInfo.description,
            score,
            reason: reason || '基于知识图谱推荐',
            algorithm: 'knowledge_based',
            confidence: score,
            generatedAt: new Date(),
            metadata: {
              knowledgeLevel: userKnowledge?.level,
              prerequisitesMet: prerequisiteMatch > 0.8,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
    } catch (error) {
      console.error('基于知识的推荐失败:', error)
      return []
    }
  }
  
  /**
   * 深度学习推荐
   */
  private async generateDeepLearning(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { userId, limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取用户嵌入向量
      const userEmbedding = await this.getUserEmbedding(userId)
      if (!userEmbedding) {
        return recommendations
      }

      // 获取候选项目
      const candidateItems = await this.getCandidateItems(context)

      // 计算相似度分数
      const itemScores: Array<{itemId: string, score: number}> = []

      for (const item of candidateItems) {
        const itemEmbedding = await this.getItemEmbedding(item.id)
        if (!itemEmbedding) continue

        // 计算余弦相似度
        const similarity = this.calculateCosineSimilarity(userEmbedding, itemEmbedding)

        // 使用神经网络预测评分（模拟）
        const predictedRating = await this.predictRating(userId, item.id, similarity)

        if (predictedRating > 0.5) {
          itemScores.push({ itemId: item.id, score: predictedRating })
        }
      }

      // 排序并返回推荐结果
      const sortedItems = itemScores
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

      for (const { itemId, score } of sortedItems) {
        const itemInfo = await this.getItemInfo(itemId)
        if (itemInfo) {
          recommendations.push({
            id: itemId,
            itemId,
            type: 'content' as any,
            title: itemInfo.title,
            description: itemInfo.description,
            score,
            reason: '基于深度学习模型推荐',
            algorithm: 'deep_learning',
            confidence: score,
            generatedAt: new Date(),
            metadata: {
              neuralNetworkScore: score,
              embeddingSimilarity: score,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
    } catch (error) {
      console.error('深度学习推荐失败:', error)
      return []
    }
  }
  
  /**
   * 人工推荐
   */
  private async generateManual(
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    try {
      const { userId, limit = 10 } = context
      const recommendations: RecommendationItem[] = []

      // 获取专家推荐规则
      const expertRules = await this.getExpertRules()

      // 获取用户画像
      const userProfile = await this.getUserProfile(userId)

      // 获取手工策划的内容
      const curatedContent = await this.getCuratedContent()

      for (const content of curatedContent) {
        const itemInfo = await this.getItemInfo(content.id)
        if (!itemInfo) continue

        let score = content.baseScore
        let reason = content.reason

        // 应用专家规则
        for (const rule of expertRules) {
          if (this.matchesRule(rule, userProfile, content)) {
            score += rule.scoreBoost
            reason += `; ${rule.description}`
          }
        }

        // 时间敏感性调整
        if (content.isTimeSensitive) {
          const timeBoost = this.calculateTimeBoost(content.publishedAt)
          score += timeBoost
          if (timeBoost > 0.1) {
            reason += '; 时效性内容'
          }
        }

        if (score > 0.3) {
          recommendations.push({
            id: content.id,
            itemId: content.id,
            type: content.type as any,
            title: itemInfo.title,
            description: itemInfo.description,
            score,
            reason,
            algorithm: 'manual',
            confidence: 0.9, // 人工推荐置信度较高
            generatedAt: new Date(),
            metadata: {
              expertCurated: true,
              curatorId: content.curatorId,
              ...itemInfo.metadata
            }
          })
        }
      }

      return recommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
    } catch (error) {
      console.error('人工推荐失败:', error)
      return []
    }
  }
  
  /**
   * 合并和排序推荐
   */
  private mergeAndRankRecommendations(
    recommendations: RecommendationItem[],
    context: RecommendationContext
  ): RecommendationItem[] {
    // 去重
    const uniqueRecommendations = new Map<string, RecommendationItem>()
    
    for (const item of recommendations) {
      const existing = uniqueRecommendations.get(item.id)
      if (!existing || item.score > existing.score) {
        uniqueRecommendations.set(item.id, item)
      }
    }
    
    // 排序
    return Array.from(uniqueRecommendations.values())
      .filter(item => item.confidence >= this.config.minConfidence)
      .sort((a, b) => b.score - a.score)
  }
  
  /**
   * 应用多样性过滤
   */
  private applyDiversityFilter(
    recommendations: RecommendationItem[],
    diversityFactor: number
  ): RecommendationItem[] {
    if (diversityFactor === 0) return recommendations
    
    const result: RecommendationItem[] = []
    const typeCount = new Map<string, number>()
    
    for (const item of recommendations) {
      const currentCount = typeCount.get(item.type) || 0
      const maxAllowed = Math.ceil(recommendations.length * diversityFactor)
      
      if (currentCount < maxAllowed) {
        result.push(item)
        typeCount.set(item.type, currentCount + 1)
      }
    }
    
    return result
  }
  
  /**
   * 更新用户反馈
   */
  async updateFeedback(
    userId: Entity,
    itemId: string,
    feedback: 'like' | 'dislike' | 'view' | 'click' | 'complete'
  ): Promise<void> {
    const userKey = userId.toString()
    if (!this.userFeedback.has(userKey)) {
      this.userFeedback.set(userKey, new Map())
    }
    
    const userFeedbackMap = this.userFeedback.get(userKey)!
    if (!userFeedbackMap.has(itemId)) {
      userFeedbackMap.set(itemId, [])
    }
    
    userFeedbackMap.get(itemId)!.push(feedback)
    
    // 清除相关缓存
    this.clearUserCache(userId)
  }
  
  /**
   * 获取推荐解释
   */
  async explainRecommendation(
    userId: Entity,
    itemId: string
  ): Promise<string> {
    try {
      // 获取用户历史和偏好
      const userHistory = await this.getUserHistory(userId)
      const userPreferences = await this.getUserPreferences(userId)
      const userProfile = await this.getUserProfile(userId)

      // 获取项目信息和特征
      const itemInfo = await this.getItemInfo(itemId)
      const itemFeatures = await this.getContentFeatures(itemId)

      if (!itemInfo || !itemFeatures) {
        return '推荐基于系统算法分析。'
      }

      const explanations: string[] = []

      // 基于用户偏好的解释
      if (userPreferences) {
        if (userPreferences.preferredCategories.includes(itemFeatures.category)) {
          explanations.push(`这是您偏好的${itemFeatures.category}类别内容`)
        }

        const matchingTags = itemFeatures.tags.filter(tag =>
          userPreferences.preferredTags.includes(tag)
        )
        if (matchingTags.length > 0) {
          explanations.push(`包含您感兴趣的标签：${matchingTags.join('、')}`)
        }

        const difficultyMatch = Math.abs(userPreferences.difficultyLevel - itemFeatures.difficulty)
        if (difficultyMatch <= 1) {
          explanations.push('难度适合您当前的学习水平')
        }
      }

      // 基于学习历史的解释
      if (userHistory.length > 0) {
        const similarItems = userHistory.filter(h => {
          return itemFeatures.topics.some(topic =>
            h.itemId.toLowerCase().includes(topic.toLowerCase())
          )
        })

        if (similarItems.length > 0) {
          explanations.push('与您之前学习的内容相关')
        }

        const completedItems = userHistory.filter(h => h.interactionType === 'complete')
        if (completedItems.length > 0) {
          explanations.push('基于您的学习完成记录推荐')
        }
      }

      // 基于用户画像的解释
      if (userProfile) {
        if (userProfile.learningStyle === 'visual' && itemFeatures.keywords.includes('visual')) {
          explanations.push('适合您的视觉学习风格')
        }

        const interestMatch = userProfile.interests.filter(interest =>
          itemFeatures.keywords.some(keyword =>
            keyword.toLowerCase().includes(interest.toLowerCase())
          )
        )
        if (interestMatch.length > 0) {
          explanations.push(`符合您的兴趣：${interestMatch.join('、')}`)
        }
      }

      // 基于内容特征的解释
      if (itemFeatures.duration <= 30) {
        explanations.push('学习时间较短，适合碎片化学习')
      }

      if (itemFeatures.difficulty <= 2) {
        explanations.push('内容难度较低，容易上手')
      }

      // 构建最终解释
      if (explanations.length === 0) {
        return '这是根据智能算法为您精选的优质内容。'
      }

      const mainExplanation = explanations.slice(0, 3).join('；')
      return `推荐理由：${mainExplanation}。`

    } catch (error) {
      console.error('生成推荐解释失败:', error)
      return '基于您的学习历史和偏好，我们推荐了这个内容。'
    }
  }
  
  /**
   * 生成缓存键
   */
  private generateCacheKey(context: RecommendationContext): string {
    return `rec_${context.userId}_${JSON.stringify(context)}`
  }
  
  /**
   * 获取缓存结果
   */
  private getCachedResult(cacheKey: string): RecommendationResult | null {
    const cached = this.cache.get(cacheKey)
    if (!cached) return null
    
    const now = Date.now()
    if (now - cached.timestamp > this.config.cacheTimeout * 1000) {
      this.cache.delete(cacheKey)
      return null
    }
    
    return cached.result
  }
  
  /**
   * 缓存结果
   */
  private cacheResult(cacheKey: string, result: RecommendationResult): void {
    this.cache.set(cacheKey, {
      result,
      timestamp: Date.now()
    })
  }
  
  /**
   * 清除用户缓存
   */
  private clearUserCache(userId: Entity): void {
    const userPrefix = `rec_${userId}_`
    for (const key of this.cache.keys()) {
      if (key.startsWith(userPrefix)) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取用户历史行为数据
   */
  private async getUserHistory(userId: Entity): Promise<UserHistoryItem[]> {
    try {
      // 模拟从数据库获取用户历史数据
      // 实际实现中应该从数据库或API获取
      return [
        {
          itemId: 'item1',
          rating: 5,
          timestamp: new Date(),
          interactionType: 'view'
        },
        {
          itemId: 'item2',
          rating: 4,
          timestamp: new Date(),
          interactionType: 'complete'
        }
      ]
    } catch (error) {
      console.error('获取用户历史失败:', error)
      return []
    }
  }

  /**
   * 查找相似用户
   */
  private async findSimilarUsers(
    userId: Entity,
    userHistory: UserHistoryItem[]
  ): Promise<SimilarUser[]> {
    try {
      // 模拟相似用户查找算法
      // 实际实现中应该使用余弦相似度或皮尔逊相关系数
      return [
        {
          userId: 2 as Entity,
          similarity: 0.8
        },
        {
          userId: 3 as Entity,
          similarity: 0.7
        }
      ]
    } catch (error) {
      console.error('查找相似用户失败:', error)
      return []
    }
  }

  /**
   * 获取项目信息
   */
  private async getItemInfo(itemId: string): Promise<ItemInfo | null> {
    try {
      // 模拟从数据库获取项目信息
      return {
        title: `项目 ${itemId}`,
        description: `项目 ${itemId} 的描述`,
        metadata: {
          category: 'education',
          difficulty: 'medium'
        }
      }
    } catch (error) {
      console.error('获取项目信息失败:', error)
      return null
    }
  }

  /**
   * 获取用户偏好
   */
  private async getUserPreferences(userId: Entity): Promise<UserPreferences | null> {
    try {
      // 模拟从用户配置文件获取偏好
      return {
        preferredCategories: ['programming', 'mathematics'],
        preferredTags: ['beginner', 'interactive'],
        difficultyLevel: 3,
        interests: ['web development', 'algorithms']
      }
    } catch (error) {
      console.error('获取用户偏好失败:', error)
      return null
    }
  }

  /**
   * 获取内容特征
   */
  private async getContentFeatures(contentId: string): Promise<ContentFeatures | null> {
    try {
      // 模拟从内容数据库获取特征
      return {
        category: 'programming',
        tags: ['javascript', 'beginner'],
        difficulty: 3,
        duration: 60,
        topics: ['variables', 'functions'],
        keywords: ['javascript', 'programming', 'web']
      }
    } catch (error) {
      console.error('获取内容特征失败:', error)
      return null
    }
  }

  /**
   * 获取候选项目
   */
  private async getCandidateItems(context: RecommendationContext): Promise<CandidateItem[]> {
    try {
      // 模拟获取候选项目列表
      return [
        { id: 'item1', type: 'course' },
        { id: 'item2', type: 'lesson' },
        { id: 'item3', type: 'exercise' }
      ]
    } catch (error) {
      console.error('获取候选项目失败:', error)
      return []
    }
  }

  /**
   * 计算偏好分数
   */
  private calculatePreferenceScore(
    preferences: UserPreferences,
    features: ContentFeatures
  ): number {
    let score = 0

    // 类别匹配
    if (preferences.preferredCategories.includes(features.category)) {
      score += 0.3
    }

    // 标签匹配
    const tagMatches = features.tags.filter(tag =>
      preferences.preferredTags.includes(tag)
    ).length
    score += (tagMatches / Math.max(features.tags.length, 1)) * 0.3

    // 难度匹配
    const difficultyDiff = Math.abs(preferences.difficultyLevel - features.difficulty)
    score += Math.max(0, (5 - difficultyDiff) / 5) * 0.2

    // 兴趣匹配
    const interestMatches = features.keywords.filter(keyword =>
      preferences.interests.some(interest =>
        interest.toLowerCase().includes(keyword.toLowerCase())
      )
    ).length
    score += (interestMatches / Math.max(features.keywords.length, 1)) * 0.2

    return Math.min(score, 1)
  }

  /**
   * 计算内容相似度
   */
  private calculateContentSimilarity(
    content1: ContentFeatures,
    content2: ContentFeatures
  ): number {
    let similarity = 0

    // 类别相似度
    if (content1.category === content2.category) {
      similarity += 0.3
    }

    // 标签相似度
    const commonTags = content1.tags.filter(tag => content2.tags.includes(tag))
    const tagSimilarity = commonTags.length /
      Math.max(content1.tags.length + content2.tags.length - commonTags.length, 1)
    similarity += tagSimilarity * 0.3

    // 难度相似度
    const difficultyDiff = Math.abs(content1.difficulty - content2.difficulty)
    similarity += Math.max(0, (5 - difficultyDiff) / 5) * 0.2

    // 主题相似度
    const commonTopics = content1.topics.filter(topic => content2.topics.includes(topic))
    const topicSimilarity = commonTopics.length /
      Math.max(content1.topics.length + content2.topics.length - commonTopics.length, 1)
    similarity += topicSimilarity * 0.2

    return Math.min(similarity, 1)
  }

  /**
   * 获取热门项目
   */
  private async getPopularItems(limit: number): Promise<PopularItem[]> {
    try {
      // 模拟从数据库获取热门项目
      return [
        {
          id: 'popular1',
          type: 'course',
          viewCount: 1000,
          averageRating: 4.5,
          popularityScore: 0.9
        },
        {
          id: 'popular2',
          type: 'lesson',
          viewCount: 800,
          averageRating: 4.2,
          popularityScore: 0.8
        },
        {
          id: 'popular3',
          type: 'exercise',
          viewCount: 600,
          averageRating: 4.0,
          popularityScore: 0.7
        }
      ].slice(0, limit)
    } catch (error) {
      console.error('获取热门项目失败:', error)
      return []
    }
  }

  /**
   * 获取用户知识水平
   */
  private async getUserKnowledgeLevel(userId: Entity): Promise<UserKnowledge | null> {
    try {
      // 模拟从学习分析系统获取用户知识水平
      return {
        level: 3,
        masteredTopics: ['variables', 'functions', 'loops'],
        weakTopics: ['recursion', 'algorithms'],
        learningStyle: 'visual'
      }
    } catch (error) {
      console.error('获取用户知识水平失败:', error)
      return null
    }
  }

  /**
   * 获取学习规则
   */
  private async getLearningRules(): Promise<LearningRule[]> {
    try {
      // 模拟从知识库获取学习规则
      return [
        {
          id: 'rule1',
          condition: 'beginner',
          recommendation: 'start_with_basics',
          weight: 1.0
        }
      ]
    } catch (error) {
      console.error('获取学习规则失败:', error)
      return []
    }
  }

  /**
   * 计算目标匹配度
   */
  private calculateGoalMatch(goals: string[], features: ContentFeatures): number {
    const matchingTopics = features.topics.filter(topic =>
      goals.some(goal => goal.toLowerCase().includes(topic.toLowerCase()))
    )
    return matchingTopics.length / Math.max(goals.length, 1)
  }

  /**
   * 计算难度匹配度
   */
  private calculateDifficultyMatch(
    preference: string,
    contentDifficulty: number,
    userLevel: number
  ): number {
    let targetDifficulty = userLevel

    switch (preference) {
      case 'easy':
        targetDifficulty = Math.max(1, userLevel - 1)
        break
      case 'hard':
        targetDifficulty = Math.min(5, userLevel + 1)
        break
      case 'adaptive':
        targetDifficulty = userLevel
        break
    }

    const diff = Math.abs(contentDifficulty - targetDifficulty)
    return Math.max(0, (3 - diff) / 3)
  }

  /**
   * 检查前置条件
   */
  private checkPrerequisites(
    userKnowledge: UserKnowledge | null,
    features: ContentFeatures
  ): number {
    if (!userKnowledge) return 0.5

    // 简化的前置条件检查
    const requiredTopics = features.topics.slice(0, 2) // 假设前两个主题是前置条件
    const metPrerequisites = requiredTopics.filter(topic =>
      userKnowledge.masteredTopics.includes(topic)
    )

    return metPrerequisites.length / Math.max(requiredTopics.length, 1)
  }

  /**
   * 获取用户嵌入向量
   */
  private async getUserEmbedding(userId: Entity): Promise<number[] | null> {
    try {
      // 模拟从深度学习模型获取用户嵌入向量
      return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
    } catch (error) {
      console.error('获取用户嵌入向量失败:', error)
      return null
    }
  }

  /**
   * 获取项目嵌入向量
   */
  private async getItemEmbedding(itemId: string): Promise<number[] | null> {
    try {
      // 模拟从深度学习模型获取项目嵌入向量
      return [0.2, 0.3, 0.1, 0.5, 0.4, 0.7, 0.6, 0.8]
    } catch (error) {
      console.error('获取项目嵌入向量失败:', error)
      return null
    }
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      return 0
    }

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      norm1 += vector1[i] * vector1[i]
      norm2 += vector2[i] * vector2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  /**
   * 预测评分
   */
  private async predictRating(
    userId: Entity,
    itemId: string,
    similarity: number
  ): Promise<number> {
    try {
      // 模拟神经网络预测评分
      // 实际实现中应该调用训练好的深度学习模型
      const baseScore = similarity * 0.8
      const randomFactor = Math.random() * 0.2
      return Math.min(1, baseScore + randomFactor)
    } catch (error) {
      console.error('预测评分失败:', error)
      return 0
    }
  }

  /**
   * 获取专家规则
   */
  private async getExpertRules(): Promise<ExpertRule[]> {
    try {
      // 模拟从专家系统获取推荐规则
      return [
        {
          id: 'rule1',
          condition: 'beginner_programmer',
          scoreBoost: 0.2,
          description: '适合编程初学者'
        },
        {
          id: 'rule2',
          condition: 'visual_learner',
          scoreBoost: 0.15,
          description: '适合视觉学习者'
        }
      ]
    } catch (error) {
      console.error('获取专家规则失败:', error)
      return []
    }
  }

  /**
   * 获取用户画像
   */
  private async getUserProfile(userId: Entity): Promise<UserProfile | null> {
    try {
      // 模拟从用户画像系统获取数据
      return {
        learningLevel: 'beginner',
        learningStyle: 'visual',
        interests: ['programming', 'web development'],
        activeHours: [9, 10, 11, 14, 15, 16],
        preferredLanguage: 'zh'
      }
    } catch (error) {
      console.error('获取用户画像失败:', error)
      return null
    }
  }

  /**
   * 获取策划内容
   */
  private async getCuratedContent(): Promise<CuratedContent[]> {
    try {
      // 模拟从内容管理系统获取策划内容
      return [
        {
          id: 'curated1',
          type: 'course',
          baseScore: 0.8,
          reason: '编辑推荐',
          curatorId: 'expert1',
          isTimeSensitive: false,
          publishedAt: new Date()
        },
        {
          id: 'curated2',
          type: 'lesson',
          baseScore: 0.7,
          reason: '热门课程',
          curatorId: 'expert2',
          isTimeSensitive: true,
          publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1天前
        }
      ]
    } catch (error) {
      console.error('获取策划内容失败:', error)
      return []
    }
  }

  /**
   * 检查规则匹配
   */
  private matchesRule(
    rule: ExpertRule,
    userProfile: UserProfile | null,
    content: CuratedContent
  ): boolean {
    if (!userProfile) return false

    switch (rule.condition) {
      case 'beginner_programmer':
        return userProfile.learningLevel === 'beginner' &&
               userProfile.interests.includes('programming')
      case 'visual_learner':
        return userProfile.learningStyle === 'visual'
      default:
        return false
    }
  }

  /**
   * 计算时间加成
   */
  private calculateTimeBoost(publishedAt: Date): number {
    const now = new Date()
    const hoursSincePublished = (now.getTime() - publishedAt.getTime()) / (1000 * 60 * 60)

    // 24小时内的内容有时间加成
    if (hoursSincePublished <= 24) {
      return 0.2 * (1 - hoursSincePublished / 24)
    }

    return 0
  }
}

// 辅助接口定义
interface UserHistoryItem {
  itemId: string
  rating?: number
  timestamp: Date
  interactionType: 'view' | 'like' | 'complete' | 'share'
}

interface SimilarUser {
  userId: Entity
  similarity: number
}

interface ItemInfo {
  title: string
  description: string
  metadata: Record<string, any>
}

interface ContentFeatures {
  category: string
  tags: string[]
  difficulty: number
  duration: number
  topics: string[]
  keywords: string[]
}

interface UserPreferences {
  preferredCategories: string[]
  preferredTags: string[]
  difficultyLevel: number
  interests: string[]
}

interface CandidateItem {
  id: string
  type: string
  score?: number
}

interface PopularItem {
  id: string
  type: string
  viewCount: number
  averageRating: number
  popularityScore: number
}

interface UserKnowledge {
  level: number
  masteredTopics: string[]
  weakTopics: string[]
  learningStyle: string
}

interface LearningRule {
  id: string
  condition: string
  recommendation: string
  weight: number
}

interface ExpertRule {
  id: string
  condition: string
  scoreBoost: number
  description: string
}

interface UserProfile {
  learningLevel: string
  learningStyle: string
  interests: string[]
  activeHours: number[]
  preferredLanguage: string
}

interface CuratedContent {
  id: string
  type: string
  baseScore: number
  reason: string
  curatorId: string
  isTimeSensitive: boolean
  publishedAt: Date
}
