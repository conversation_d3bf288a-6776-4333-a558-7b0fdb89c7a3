# DL-Engine 资产系统集成指南

本文档详细说明了如何将资产系统集成到DL-Engine中，以及如何在项目中使用完整的资产管理功能。

## 🚀 集成概述

资产系统已完全集成到DL-Engine的核心架构中，包括：

- **状态管理**: 基于hookstate的响应式状态管理
- **ECS集成**: 资产相关的组件和系统
- **自动初始化**: 引擎启动时自动初始化资产系统
- **性能监控**: 内置的性能监控和优化建议

## 📋 集成组件

### 1. 核心模块

```typescript
// 资产管理器 - 统一的资产管理入口
AssetManager

// 资产缓存 - 智能缓存系统
AssetCache

// 资产加载器 - 可扩展的加载器架构
AssetLoader, LoaderManager

// 专用加载器
TextureLoader, GLTFLoader, AudioLoader

// 状态管理
AssetState, AssetStateUtils
```

### 2. ECS组件

```typescript
// 资产引用组件
AssetReferenceComponent

// 专用资产组件
ModelAssetComponent
TextureAssetComponent
AudioAssetComponent

// 管理组件
AssetLoaderComponent
AssetCacheComponent
AssetPreloaderComponent
AssetDependencyComponent
AssetPerformanceComponent
```

### 3. ECS系统

```typescript
// 资产加载系统
AssetLoadingSystem

// 资产预加载系统
AssetPreloadingSystem

// 资产依赖系统
AssetDependencySystem

// 资产性能监控系统
AssetPerformanceSystem
```

## 🔧 配置选项

### 引擎配置

```typescript
import { initializeDLEngine, DLEnginePresets } from '@dl-engine/engine-core'

// 使用预设配置
await initializeDLEngine(DLEnginePresets.development())

// 自定义配置
await initializeDLEngine({
  assets: {
    maxConcurrentLoads: 8,
    cache: {
      maxSize: 1024 * 1024 * 1024, // 1GB
      strategy: CacheStrategy.LRU,
      defaultTTL: 60 * 60 * 1000, // 1小时
      enablePersistence: true
    },
    defaultTimeout: 30000,
    defaultRetries: 3,
    enablePreloading: true,
    enableAutoCleanup: true
  }
})
```

### 预设配置

#### 开发模式
- 缓存大小: 1GB
- 并发加载: 8个
- 启用持久化缓存
- 启用预加载和自动清理

#### 生产模式
- 缓存大小: 512MB
- 并发加载: 4个
- 启用持久化缓存
- 启用预加载和自动清理

#### 移动设备模式
- 缓存大小: 128MB
- 并发加载: 2个
- 禁用持久化缓存
- 禁用预加载，启用自动清理

## 💻 使用示例

### 基础使用

```typescript
import { AssetManager, AssetType, AssetPriority } from '@dl-engine/engine-core/assets'

// 获取资产管理器实例
const assetManager = AssetManager.getInstance()

// 加载纹理
const texture = await assetManager.load(
  'my-texture',
  '/assets/textures/diffuse.jpg',
  AssetType.TEXTURE,
  {
    priority: AssetPriority.HIGH,
    onProgress: (progress) => console.log(`进度: ${progress * 100}%`)
  }
)

// 使用纹理
const material = new MeshBasicMaterial({ map: texture.data })
```

### ECS集成使用

```typescript
import { createEntity } from '@dl-engine/engine-ecs'
import { AssetComponentUtils, AssetReferenceComponent } from '@dl-engine/engine-core/assets'

// 创建实体
const entity = createEntity()

// 添加资产引用组件
AssetComponentUtils.createAssetReference(
  entity,
  'character-model',
  '/assets/models/character.glb',
  AssetType.MODEL,
  {
    priority: AssetPriority.HIGH,
    autoLoad: true
  }
)

// 检查加载状态
const isLoaded = AssetComponentUtils.isAssetLoaded(entity)
const progress = AssetComponentUtils.getAssetLoadProgress(entity)
```

### 批量加载

```typescript
// 定义资产列表
const assetsToLoad = [
  {
    id: 'character',
    url: '/assets/models/character.glb',
    type: AssetType.MODEL
  },
  {
    id: 'environment',
    url: '/assets/textures/environment.hdr',
    type: AssetType.TEXTURE
  },
  {
    id: 'music',
    url: '/assets/audio/background.mp3',
    type: AssetType.AUDIO
  }
]

// 批量加载
const assets = await assetManager.loadBatch(assetsToLoad)
console.log(`加载完成: ${assets.length} 个资产`)
```

### 预加载

```typescript
import { AssetPreloaderComponent } from '@dl-engine/engine-core/assets'

// 为实体添加预加载组件
AssetPreloaderComponent.set(entity, {
  preloadAssets: [
    {
      id: 'next-level-model',
      url: '/assets/models/level2.glb',
      type: AssetType.MODEL,
      priority: AssetPriority.LOW
    }
  ],
  triggerCondition: 'onProximity',
  triggerDistance: 100,
  hasTriggered: false,
  preloadProgress: 0,
  isPreloadComplete: false,
  preloadStartTime: 0,
  preloadEndTime: 0
})
```

### 性能监控

```typescript
import { AssetPerformanceComponent } from '@dl-engine/engine-core/assets'

// 添加性能监控组件
AssetPerformanceComponent.set(entity, {
  assetId: 'large-model',
  loadTime: 0,
  fileSize: 0,
  memoryUsage: 0,
  accessCount: 0,
  lastAccessTime: 0,
  averageAccessInterval: 0,
  isBottleneck: false,
  performanceScore: 100,
  optimizationSuggestions: []
})

// 获取性能统计
const stats = assetManager.getStats()
console.log('总资产数:', stats.totalAssets)
console.log('缓存使用率:', stats.cacheSize / stats.totalSize)
```

## 🎯 最佳实践

### 1. 资产组织

```typescript
// 按类型组织资产
const ASSET_PATHS = {
  models: '/assets/models/',
  textures: '/assets/textures/',
  audio: '/assets/audio/',
  ui: '/assets/ui/'
}

// 使用命名约定
const assetId = `${category}-${name}-${variant}`
// 例如: 'character-hero-idle', 'texture-wall-brick'
```

### 2. 优先级管理

```typescript
// 关键资产使用高优先级
await assetManager.load('ui-loading', '/ui/loading.png', AssetType.TEXTURE, {
  priority: AssetPriority.CRITICAL
})

// 装饰性资产使用低优先级
await assetManager.load('decoration', '/models/plant.glb', AssetType.MODEL, {
  priority: AssetPriority.LOW
})
```

### 3. 内存管理

```typescript
// 定期清理不需要的资产
setInterval(() => {
  const cleaned = assetManager.cleanup()
  console.log(`清理了 ${cleaned} 个过期资产`)
}, 60000) // 每分钟清理一次

// 在场景切换时卸载资产
function switchScene() {
  // 卸载当前场景的资产
  assetManager.unload('current-scene-model')
  assetManager.unload('current-scene-texture')
  
  // 加载新场景的资产
  loadNewSceneAssets()
}
```

### 4. 错误处理

```typescript
try {
  const asset = await assetManager.load('critical-asset', '/path/to/asset', AssetType.MODEL)
} catch (error) {
  console.error('关键资产加载失败，使用备用资产:', error)
  
  // 加载备用资产
  const fallback = await assetManager.load(
    'fallback-asset',
    '/assets/fallbacks/default.glb',
    AssetType.MODEL
  )
}
```

### 5. 性能优化

```typescript
// 使用压缩格式
const model = await assetManager.load('model', '/assets/model.glb', AssetType.MODEL, {
  enableDraco: true,
  enableKTX2: true
})

// 预加载关键资产
await assetManager.preload('next-level', '/assets/level2.glb', AssetType.MODEL)

// 监控性能
const cacheStats = assetManager.getCacheStats()
if (cacheStats.utilization > 0.9) {
  console.warn('缓存使用率过高，考虑清理或增加缓存大小')
}
```

## 🔍 调试和监控

### 资产统计

```typescript
// 获取详细统计信息
const stats = assetManager.getStats()
console.table({
  '总资产数': stats.totalAssets,
  '总大小': `${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`,
  '缓存大小': `${(stats.cacheSize / 1024 / 1024).toFixed(2)} MB`,
  '缓存命中率': `${(stats.cacheHitRate * 100).toFixed(2)}%`,
  '平均加载时间': `${stats.averageLoadTime.toFixed(2)} ms`
})

// 按类型统计
Object.entries(stats.assetsByType).forEach(([type, count]) => {
  console.log(`${type}: ${count} 个`)
})
```

### 缓存监控

```typescript
// 获取缓存详情
const cacheStats = assetManager.getCacheStats()
console.log('缓存详情:', {
  size: cacheStats.size,
  utilization: `${(cacheStats.utilization * 100).toFixed(2)}%`,
  hitRate: `${(cacheStats.hitRate * 100).toFixed(2)}%`
})

// 查看缓存项
cacheStats.items.forEach(item => {
  console.log(`${item.id}: ${(item.size / 1024).toFixed(2)} KB, 访问 ${item.accessCount} 次`)
})
```

### 性能分析

```typescript
// 查询性能瓶颈
const bottlenecks = assetManager.query({
  // 这里需要扩展查询API来支持性能查询
})

// 获取优化建议
const performanceEntities = defineQuery([AssetPerformanceComponent])()
for (const entity of performanceEntities) {
  if (hasComponent(entity, AssetPerformanceComponent)) {
    const perf = getComponent(entity, AssetPerformanceComponent)
    if (perf?.isBottleneck) {
      console.warn(`性能瓶颈: ${perf.assetId}`)
      console.log('优化建议:', perf.optimizationSuggestions)
    }
  }
}
```

## 🚨 故障排除

### 常见问题

1. **资产加载失败**
   - 检查URL是否正确
   - 验证网络连接
   - 查看浏览器控制台错误

2. **缓存问题**
   - 清理浏览器缓存
   - 检查缓存配置
   - 验证缓存大小限制

3. **性能问题**
   - 监控内存使用
   - 检查并发加载数
   - 优化资产大小

4. **ECS集成问题**
   - 确保组件正确添加
   - 检查系统执行顺序
   - 验证实体状态

### 调试工具

```typescript
// 启用详细日志
const assetManager = AssetManager.getInstance({
  debug: true,
  verbose: true
})

// 监听资产事件
assetManager.on('assetLoaded', (asset) => {
  console.log('资产加载完成:', asset.name)
})

assetManager.on('assetError', (error, assetId) => {
  console.error('资产加载失败:', assetId, error)
})

// 导出资产状态用于调试
window.debugAssets = {
  manager: assetManager,
  stats: () => assetManager.getStats(),
  cache: () => assetManager.getCacheStats(),
  query: (options) => assetManager.query(options)
}
```

## 📚 API参考

详细的API文档请参考各个模块的TypeScript类型定义和README文档。

## 🔄 版本更新

资产系统会随着引擎版本持续更新和优化。请关注：

- 新的资产格式支持
- 性能优化改进
- 新的ECS组件和系统
- API增强和改进

---

资产系统现已完全集成到DL-Engine中，提供了企业级的资产管理能力！🎉
