/**
 * DL-Engine 纹理加载器
 * 加载各种格式的纹理资产
 */

import {
  Texture,
  TextureLoader as ThreeTextureLoader,
  CubeTextureLoader,
  DataTexture,
  RGBAFormat,
  UnsignedByteType,
  LinearFilter,
  ClampToEdgeWrapping,
  SRGBColorSpace,
  NoColorSpace,
  MagnificationTextureFilter,
  MinificationTextureFilter,
  Wrapping
} from 'three'
import { AssetLoader } from '../AssetLoader'
import { 
  Asset, 
  AssetType, 
  AssetLoadOptions, 
  TextureAsset 
} from '../AssetTypes'

/**
 * 纹理加载选项
 */
export interface TextureLoadOptions extends AssetLoadOptions {
  /** 是否翻转Y轴 */
  flipY?: boolean
  
  /** 是否生成Mipmap */
  generateMipmaps?: boolean
  
  /** 纹理颜色空间 */
  colorSpace?: string
  
  /** 过滤方式 */
  minFilter?: MinificationTextureFilter
  magFilter?: MagnificationTextureFilter

  /** 包装方式 */
  wrapS?: Wrapping
  wrapT?: Wrapping
  
  /** 是否为立方体纹理 */
  isCubeTexture?: boolean
  
  /** 立方体纹理的6个面URL */
  cubeUrls?: string[]
}

/**
 * 纹理加载器
 */
export class TextureLoader extends AssetLoader<TextureAsset> {
  private threeLoader: ThreeTextureLoader
  private cubeLoader: CubeTextureLoader

  constructor() {
    super()
    this.supportedTypes = [AssetType.TEXTURE, AssetType.IMAGE]
    this.threeLoader = new ThreeTextureLoader()
    this.cubeLoader = new CubeTextureLoader()
  }

  /**
   * 执行纹理加载
   */
  protected async performLoad(
    asset: Asset, 
    options: TextureLoadOptions = {}
  ): Promise<TextureAsset> {
    const progressUpdater = this.createProgressUpdater(asset.id, options)
    
    try {
      let texture: Texture

      if (options.isCubeTexture && options.cubeUrls) {
        // 加载立方体纹理
        texture = await this.loadCubeTexture(options.cubeUrls, options, progressUpdater)
      } else {
        // 加载普通纹理
        texture = await this.loadTexture(asset.url, options, progressUpdater)
      }

      // 应用纹理设置
      this.applyTextureSettings(texture, options)

      // 计算纹理大小
      const size = this.calculateTextureSize(texture)

      // 创建纹理资产
      const textureAsset: TextureAsset = {
        ...asset,
        type: AssetType.TEXTURE,
        data: texture,
        size,
        metadata: {
          width: texture.image?.width || 0,
          height: texture.image?.height || 0,
          format: this.getTextureFormat(texture),
          colorSpace: texture.colorSpace,
          flipY: texture.flipY,
          generateMipmaps: texture.generateMipmaps
        }
      }

      return textureAsset

    } catch (error) {
      throw new Error(`纹理加载失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 加载普通纹理
   */
  private async loadTexture(
    url: string, 
    options: TextureLoadOptions,
    onProgress: (loaded: number, total: number) => void
  ): Promise<Texture> {
    return new Promise((resolve, reject) => {
      this.threeLoader.load(
        url,
        (texture) => {
          onProgress(1, 1) // 完成
          resolve(texture)
        },
        (progress) => {
          if (progress.lengthComputable) {
            onProgress(progress.loaded, progress.total)
          }
        },
        (error) => {
          reject(error)
        }
      )
    })
  }

  /**
   * 加载立方体纹理
   */
  private async loadCubeTexture(
    urls: string[],
    options: TextureLoadOptions,
    onProgress: (loaded: number, total: number) => void
  ): Promise<Texture> {
    if (urls.length !== 6) {
      throw new Error('立方体纹理需要6个面的URL')
    }

    return new Promise((resolve, reject) => {
      let loadedCount = 0
      const totalCount = urls.length

      this.cubeLoader.load(
        urls,
        (texture) => {
          onProgress(1, 1) // 完成
          resolve(texture)
        },
        (progress) => {
          // 立方体纹理的进度计算
          loadedCount++
          onProgress(loadedCount, totalCount)
        },
        (error) => {
          reject(error)
        }
      )
    })
  }

  /**
   * 应用纹理设置
   */
  private applyTextureSettings(texture: Texture, options: TextureLoadOptions): void {
    // 翻转Y轴
    if (options.flipY !== undefined) {
      texture.flipY = options.flipY
    }

    // 生成Mipmap
    if (options.generateMipmaps !== undefined) {
      texture.generateMipmaps = options.generateMipmaps
    }

    // 颜色空间
    if (options.colorSpace !== undefined) {
      texture.colorSpace = options.colorSpace
    } else {
      // 默认使用sRGB颜色空间
      texture.colorSpace = SRGBColorSpace
    }

    // 过滤方式
    if (options.minFilter !== undefined) {
      texture.minFilter = options.minFilter
    }
    if (options.magFilter !== undefined) {
      texture.magFilter = options.magFilter
    }

    // 包装方式
    if (options.wrapS !== undefined) {
      texture.wrapS = options.wrapS
    }
    if (options.wrapT !== undefined) {
      texture.wrapT = options.wrapT
    }

    // 标记需要更新
    texture.needsUpdate = true
  }

  /**
   * 计算纹理大小
   */
  private calculateTextureSize(texture: Texture): number {
    if (!texture.image) {
      return 0
    }

    const width = texture.image.width || 0
    const height = texture.image.height || 0
    
    // 基础大小计算 (RGBA * width * height)
    let size = width * height * 4

    // 如果生成了Mipmap，增加33%的大小
    if (texture.generateMipmaps) {
      size *= 1.33
    }

    // 立方体纹理有6个面
    if ('isCubeTexture' in texture && (texture as any).isCubeTexture) {
      size *= 6
    }

    return Math.round(size)
  }

  /**
   * 获取纹理格式字符串
   */
  private getTextureFormat(texture: Texture): string {
    switch (texture.format) {
      case RGBAFormat:
        return 'RGBA'
      default:
        return 'Unknown'
    }
  }

  /**
   * 创建数据纹理
   */
  static createDataTexture(
    data: Uint8Array | Float32Array,
    width: number,
    height: number,
    format = RGBAFormat,
    type = UnsignedByteType
  ): DataTexture {
    const texture = new DataTexture(data, width, height, format, type)
    texture.needsUpdate = true
    return texture
  }

  /**
   * 创建空白纹理
   */
  static createBlankTexture(
    width = 1,
    height = 1,
    color = [255, 255, 255, 255]
  ): DataTexture {
    const data = new Uint8Array(width * height * 4)
    
    for (let i = 0; i < data.length; i += 4) {
      data[i] = color[0]     // R
      data[i + 1] = color[1] // G
      data[i + 2] = color[2] // B
      data[i + 3] = color[3] // A
    }

    return this.createDataTexture(data, width, height)
  }

  /**
   * 从Canvas创建纹理
   */
  static createFromCanvas(canvas: HTMLCanvasElement): Texture {
    const texture = new Texture(canvas)
    texture.needsUpdate = true
    return texture
  }

  /**
   * 从ImageData创建纹理
   */
  static createFromImageData(imageData: ImageData): DataTexture {
    return this.createDataTexture(
      new Uint8Array(imageData.data),
      imageData.width,
      imageData.height
    )
  }

  /**
   * 调整纹理大小
   */
  static resizeTexture(
    texture: Texture,
    newWidth: number,
    newHeight: number
  ): DataTexture {
    if (!texture.image) {
      throw new Error('纹理没有图像数据')
    }

    // 创建Canvas进行缩放
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('无法创建Canvas上下文')
    }

    canvas.width = newWidth
    canvas.height = newHeight

    // 绘制缩放后的图像
    ctx.drawImage(texture.image, 0, 0, newWidth, newHeight)

    // 获取像素数据
    const imageData = ctx.getImageData(0, 0, newWidth, newHeight)
    
    return this.createFromImageData(imageData)
  }

  /**
   * 生成法线贴图
   */
  static generateNormalMap(
    heightTexture: Texture,
    strength = 1.0
  ): DataTexture {
    if (!heightTexture.image) {
      throw new Error('高度纹理没有图像数据')
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('无法创建Canvas上下文')
    }

    const width = heightTexture.image.width
    const height = heightTexture.image.height
    
    canvas.width = width
    canvas.height = height

    // 绘制高度图
    ctx.drawImage(heightTexture.image, 0, 0)
    
    const heightData = ctx.getImageData(0, 0, width, height)
    const normalData = new Uint8Array(width * height * 4)

    // 计算法线
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4

        // 获取周围像素的高度值
        const tl = this.getHeightAt(heightData.data, x - 1, y - 1, width, height)
        const tm = this.getHeightAt(heightData.data, x, y - 1, width, height)
        const tr = this.getHeightAt(heightData.data, x + 1, y - 1, width, height)
        const ml = this.getHeightAt(heightData.data, x - 1, y, width, height)
        const mr = this.getHeightAt(heightData.data, x + 1, y, width, height)
        const bl = this.getHeightAt(heightData.data, x - 1, y + 1, width, height)
        const bm = this.getHeightAt(heightData.data, x, y + 1, width, height)
        const br = this.getHeightAt(heightData.data, x + 1, y + 1, width, height)

        // Sobel算子计算梯度
        const dx = (tr + 2 * mr + br) - (tl + 2 * ml + bl)
        const dy = (bl + 2 * bm + br) - (tl + 2 * tm + tr)

        // 计算法线向量
        const nx = -dx * strength
        const ny = -dy * strength
        const nz = 255

        // 归一化并转换到0-255范围
        const length = Math.sqrt(nx * nx + ny * ny + nz * nz)
        
        normalData[idx] = Math.round(((nx / length) + 1) * 127.5)     // R
        normalData[idx + 1] = Math.round(((ny / length) + 1) * 127.5) // G
        normalData[idx + 2] = Math.round(((nz / length) + 1) * 127.5) // B
        normalData[idx + 3] = 255 // A
      }
    }

    return this.createDataTexture(normalData, width, height)
  }

  /**
   * 获取指定位置的高度值
   */
  private static getHeightAt(
    data: Uint8ClampedArray,
    x: number,
    y: number,
    width: number,
    height: number
  ): number {
    // 边界处理
    x = Math.max(0, Math.min(width - 1, x))
    y = Math.max(0, Math.min(height - 1, y))
    
    const idx = (y * width + x) * 4
    // 使用红色通道作为高度值
    return data[idx]
  }
}
