/**
 * DL-Engine GLTF模型加载器
 * 加载GLTF/GLB格式的3D模型
 */

import { GLTFLoader as ThreeGLTFLoader, GLTF } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js'
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js'
import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js'
import * as BufferGeometryUtils from 'three/examples/jsm/utils/BufferGeometryUtils.js'
import {
  Object3D,
  AnimationClip,
  Material,
  BufferGeometry,
  Texture,
  WebGLRenderer,
  Mesh
} from 'three'
import { AssetLoader } from '../AssetLoader'
import { 
  Asset, 
  AssetType, 
  AssetLoadOptions, 
  ModelAsset 
} from '../AssetTypes'

/**
 * GLTF加载选项
 */
export interface GLTFLoadOptions extends AssetLoadOptions {
  /** 是否启用Draco压缩解码 */
  enableDraco?: boolean
  
  /** Draco解码器路径 */
  dracoDecoderPath?: string
  
  /** 是否启用KTX2纹理 */
  enableKTX2?: boolean
  
  /** KTX2解码器路径 */
  ktx2DecoderPath?: string
  
  /** 是否启用Meshopt压缩 */
  enableMeshopt?: boolean
  
  /** 是否自动播放动画 */
  autoPlayAnimations?: boolean
  
  /** 默认动画索引 */
  defaultAnimationIndex?: number
  
  /** 是否生成包围盒 */
  generateBoundingBox?: boolean
  
  /** 是否优化几何体 */
  optimizeGeometry?: boolean
  
  /** 材质覆盖 */
  materialOverrides?: Record<string, Material>
  
  /** 纹理覆盖 */
  textureOverrides?: Record<string, Texture>
}

/**
 * GLTF加载器
 */
export class GLTFLoader extends AssetLoader<ModelAsset> {
  private threeLoader: ThreeGLTFLoader
  private dracoLoader?: DRACOLoader
  private ktx2Loader?: KTX2Loader

  constructor(renderer?: WebGLRenderer) {
    super()
    this.supportedTypes = [AssetType.MODEL]
    this.threeLoader = new ThreeGLTFLoader()
    
    // 设置Meshopt解码器
    this.threeLoader.setMeshoptDecoder(MeshoptDecoder)
    
    // 如果提供了渲染器，设置KTX2加载器
    if (renderer) {
      this.setupKTX2Loader(renderer)
    }
  }

  /**
   * 设置Draco解码器
   */
  setupDracoLoader(decoderPath = '/draco/'): void {
    this.dracoLoader = new DRACOLoader()
    this.dracoLoader.setDecoderPath(decoderPath)
    this.threeLoader.setDRACOLoader(this.dracoLoader)
  }

  /**
   * 设置KTX2解码器
   */
  setupKTX2Loader(renderer: WebGLRenderer, decoderPath = '/ktx2/'): void {
    this.ktx2Loader = new KTX2Loader()
    this.ktx2Loader.setTranscoderPath(decoderPath)
    this.ktx2Loader.detectSupport(renderer)
    this.threeLoader.setKTX2Loader(this.ktx2Loader)
  }

  /**
   * 执行GLTF加载
   */
  protected async performLoad(
    asset: Asset, 
    options: GLTFLoadOptions = {}
  ): Promise<ModelAsset> {
    const progressUpdater = this.createProgressUpdater(asset.id, options)
    
    // 设置解码器
    this.setupLoaders(options)
    
    try {
      const gltf = await this.loadGLTF(asset.url, options, progressUpdater)
      
      // 处理模型数据
      const processedData = await this.processGLTF(gltf, options)
      
      // 计算模型大小
      const size = this.calculateModelSize(processedData)
      
      // 创建模型资产
      const modelAsset: ModelAsset = {
        ...asset,
        type: AssetType.MODEL,
        data: processedData,
        size,
        metadata: {
          animationCount: processedData.animations.length,
          materialCount: processedData.materials.length,
          geometryCount: processedData.geometries.length,
          textureCount: processedData.textures.length,
          nodeCount: this.countNodes(processedData.scene),
          hasAnimations: processedData.animations.length > 0,
          hasMorphTargets: this.hasMorphTargets(processedData.geometries),
          hasSkins: this.hasSkins(gltf)
        }
      }

      return modelAsset

    } catch (error) {
      throw new Error(`GLTF加载失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 设置加载器
   */
  private setupLoaders(options: GLTFLoadOptions): void {
    // 设置Draco解码器
    if (options.enableDraco && options.dracoDecoderPath) {
      this.setupDracoLoader(options.dracoDecoderPath)
    }
  }

  /**
   * 加载GLTF文件
   */
  private async loadGLTF(
    url: string,
    options: GLTFLoadOptions,
    onProgress: (loaded: number, total: number) => void
  ): Promise<GLTF> {
    return new Promise((resolve, reject) => {
      this.threeLoader.load(
        url,
        (gltf) => {
          onProgress(1, 1) // 完成
          resolve(gltf)
        },
        (progress) => {
          if (progress.lengthComputable) {
            onProgress(progress.loaded, progress.total)
          }
        },
        (error) => {
          reject(error)
        }
      )
    })
  }

  /**
   * 处理GLTF数据
   */
  private async processGLTF(
    gltf: GLTF,
    options: GLTFLoadOptions
  ): Promise<ModelAsset['data']> {
    const scene = gltf.scene.clone()
    const animations = [...gltf.animations]
    const materials: Material[] = []
    const geometries: BufferGeometry[] = []
    const textures: Texture[] = []

    // 收集材质、几何体和纹理
    scene.traverse((child) => {
      if (child instanceof Mesh) {
        const mesh = child
        
        // 收集几何体
        if (mesh.geometry && !geometries.includes(mesh.geometry)) {
          geometries.push(mesh.geometry)
          
          // 优化几何体
          if (options.optimizeGeometry) {
            this.optimizeGeometry(mesh.geometry)
          }
        }
        
        // 收集材质
        const material = Array.isArray(mesh.material) ? mesh.material : [mesh.material]
        for (const mat of material) {
          if (mat && !materials.includes(mat)) {
            materials.push(mat)
            
            // 收集纹理
            this.collectTexturesFromMaterial(mat, textures)
          }
        }
      }
    })

    // 应用材质覆盖
    if (options.materialOverrides) {
      this.applyMaterialOverrides(scene, options.materialOverrides)
    }

    // 应用纹理覆盖
    if (options.textureOverrides) {
      this.applyTextureOverrides(materials, options.textureOverrides)
    }

    // 生成包围盒
    if (options.generateBoundingBox) {
      scene.updateMatrixWorld(true)
    }

    // 处理动画
    if (options.autoPlayAnimations && animations.length > 0) {
      const animIndex = options.defaultAnimationIndex || 0
      if (animations[animIndex]) {
        // 这里可以添加动画播放逻辑
        console.log(`准备播放动画: ${animations[animIndex].name}`)
      }
    }

    return {
      scene,
      animations,
      materials,
      geometries,
      textures
    }
  }

  /**
   * 优化几何体
   */
  private optimizeGeometry(geometry: BufferGeometry): BufferGeometry {
    // 合并重复顶点
    const mergedGeometry = BufferGeometryUtils.mergeVertices(geometry)

    // 计算法线（如果没有）
    if (!mergedGeometry.attributes.normal) {
      mergedGeometry.computeVertexNormals()
    }

    // 计算包围盒和包围球
    mergedGeometry.computeBoundingBox()
    mergedGeometry.computeBoundingSphere()

    return mergedGeometry
  }

  /**
   * 从材质收集纹理
   */
  private collectTexturesFromMaterial(material: Material, textures: Texture[]): void {
    const mat = material as any
    
    // 检查常见的纹理属性
    const textureProps = [
      'map', 'normalMap', 'roughnessMap', 'metalnessMap',
      'emissiveMap', 'aoMap', 'displacementMap', 'alphaMap',
      'envMap', 'lightMap'
    ]
    
    for (const prop of textureProps) {
      const texture = mat[prop]
      if (texture && texture.isTexture && !textures.includes(texture)) {
        textures.push(texture)
      }
    }
  }

  /**
   * 应用材质覆盖
   */
  private applyMaterialOverrides(
    scene: Object3D,
    overrides: Record<string, Material>
  ): void {
    scene.traverse((child) => {
      if (child instanceof Mesh) {
        const mesh = child
        const materialName = mesh.material?.name
        
        if (materialName && overrides[materialName]) {
          mesh.material = overrides[materialName]
        }
      }
    })
  }

  /**
   * 应用纹理覆盖
   */
  private applyTextureOverrides(
    materials: Material[],
    overrides: Record<string, Texture>
  ): void {
    for (const material of materials) {
      const mat = material as any
      
      // 检查并替换纹理
      for (const [textureName, newTexture] of Object.entries(overrides)) {
        if (mat[textureName] && mat[textureName].isTexture) {
          mat[textureName] = newTexture
          mat.needsUpdate = true
        }
      }
    }
  }

  /**
   * 计算模型大小
   */
  private calculateModelSize(data: ModelAsset['data']): number {
    let size = 0
    
    // 几何体大小
    for (const geometry of data.geometries) {
      size += this.calculateGeometrySize(geometry)
    }
    
    // 纹理大小
    for (const texture of data.textures) {
      size += this.calculateTextureSize(texture)
    }
    
    // 动画数据大小（估算）
    size += data.animations.length * 1024 // 每个动画约1KB
    
    return size
  }

  /**
   * 计算几何体大小
   */
  private calculateGeometrySize(geometry: BufferGeometry): number {
    let size = 0
    
    for (const [, attribute] of Object.entries(geometry.attributes)) {
      size += attribute.array.byteLength
    }
    
    if (geometry.index) {
      size += geometry.index.array.byteLength
    }
    
    return size
  }

  /**
   * 计算纹理大小
   */
  private calculateTextureSize(texture: Texture): number {
    if (!texture.image) return 0
    
    const width = texture.image.width || 0
    const height = texture.image.height || 0
    
    // 假设RGBA格式
    let size = width * height * 4
    
    // Mipmap增加33%
    if (texture.generateMipmaps) {
      size *= 1.33
    }
    
    return Math.round(size)
  }

  /**
   * 计算节点数量
   */
  private countNodes(object: Object3D): number {
    let count = 1
    
    for (const child of object.children) {
      count += this.countNodes(child)
    }
    
    return count
  }

  /**
   * 检查是否有变形目标
   */
  private hasMorphTargets(geometries: BufferGeometry[]): boolean {
    return geometries.some(geo => geo.morphAttributes && Object.keys(geo.morphAttributes).length > 0)
  }

  /**
   * 检查是否有骨骼
   */
  private hasSkins(gltf: GLTF): boolean {
    return gltf.parser?.json?.skins && gltf.parser.json.skins.length > 0
  }

  /**
   * 销毁加载器
   */
  dispose(): void {
    if (this.dracoLoader) {
      this.dracoLoader.dispose()
    }
    if (this.ktx2Loader) {
      this.ktx2Loader.dispose()
    }
  }
}
